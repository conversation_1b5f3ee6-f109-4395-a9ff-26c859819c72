'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useCallback, useRef } from 'react'
import { useRouter } from 'next/navigation'

interface SessionPersistenceOptions {
  enableVisibilityCheck?: boolean
  enableStorageSync?: boolean
  enableAutoRefresh?: boolean
  refreshInterval?: number
  onSessionExpired?: () => void
  onSessionRestored?: () => void
}

export function useSessionPersistence(options: SessionPersistenceOptions = {}) {
  const {
    enableVisibilityCheck = true,
    enableStorageSync = true,
    enableAutoRefresh = true,
    refreshInterval = 5 * 60 * 1000, // 5分钟
    onSessionExpired,
    onSessionRestored
  } = options

  const { data: session, status, update } = useSession()
  const router = useRouter()
  const lastActivityRef = useRef(Date.now())
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const visibilityTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 更新最后活动时间
  const updateLastActivity = useCallback(() => {
    lastActivityRef.current = Date.now()
  }, [])

  // 检查会话有效性
  const checkSessionValidity = useCallback(async () => {
    try {
      const response = await fetch('/api/auth/session')
      const sessionData = await response.json()
      
      if (!sessionData?.user && session?.user) {
        // 会话已过期
        console.log('Session expired, redirecting to login')
        onSessionExpired?.()
        router.push('/auth/signin?expired=true')
      } else if (sessionData?.user && !session?.user) {
        // 会话已恢复
        console.log('Session restored')
        onSessionRestored?.()
        await update()
      }
    } catch (error) {
      console.error('Session validity check failed:', error)
    }
  }, [session, router, onSessionExpired, onSessionRestored, update])

  // 刷新会话
  const refreshSession = useCallback(async () => {
    if (status === 'authenticated') {
      try {
        await update()
        console.log('Session refreshed successfully')
      } catch (error) {
        console.error('Session refresh failed:', error)
        await checkSessionValidity()
      }
    }
  }, [status, update, checkSessionValidity])

  // 处理页面可见性变化
  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState === 'visible') {
      // 页面变为可见时检查会话
      const timeSinceLastActivity = Date.now() - lastActivityRef.current
      
      // 如果超过5分钟未活动，检查会话有效性
      if (timeSinceLastActivity > 5 * 60 * 1000) {
        checkSessionValidity()
      }
      
      updateLastActivity()
    }
  }, [checkSessionValidity, updateLastActivity])

  // 处理存储事件（多标签页同步）
  const handleStorageChange = useCallback((event: StorageEvent) => {
    if (event.key === 'session-sync') {
      const syncData = event.newValue ? JSON.parse(event.newValue) : null
      
      if (syncData?.action === 'logout' && session?.user) {
        // 其他标签页已登出
        console.log('Session logout detected from another tab')
        router.push('/auth/signin')
      } else if (syncData?.action === 'login' && !session?.user) {
        // 其他标签页已登录
        console.log('Session login detected from another tab')
        window.location.reload()
      }
    }
  }, [session, router])

  // 同步会话状态到localStorage
  const syncSessionToStorage = useCallback(() => {
    if (enableStorageSync) {
      const syncData = {
        timestamp: Date.now(),
        hasSession: !!session?.user,
        action: session?.user ? 'login' : 'logout'
      }
      
      try {
        localStorage.setItem('session-sync', JSON.stringify(syncData))
      } catch (error) {
        console.error('Failed to sync session to storage:', error)
      }
    }
  }, [session, enableStorageSync])

  // 设置自动刷新
  useEffect(() => {
    if (enableAutoRefresh && status === 'authenticated') {
      refreshTimeoutRef.current = setInterval(refreshSession, refreshInterval)
      
      return () => {
        if (refreshTimeoutRef.current) {
          clearInterval(refreshTimeoutRef.current)
        }
      }
    }
  }, [enableAutoRefresh, status, refreshSession, refreshInterval])

  // 监听页面可见性变化
  useEffect(() => {
    if (enableVisibilityCheck) {
      document.addEventListener('visibilitychange', handleVisibilityChange)
      
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
      }
    }
  }, [enableVisibilityCheck, handleVisibilityChange])

  // 监听存储变化（多标签页同步）
  useEffect(() => {
    if (enableStorageSync) {
      window.addEventListener('storage', handleStorageChange)
      
      return () => {
        window.removeEventListener('storage', handleStorageChange)
      }
    }
  }, [enableStorageSync, handleStorageChange])

  // 同步会话状态变化
  useEffect(() => {
    syncSessionToStorage()
  }, [syncSessionToStorage])

  // 监听用户活动
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    
    events.forEach(event => {
      document.addEventListener(event, updateLastActivity, { passive: true })
    })
    
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateLastActivity)
      })
    }
  }, [updateLastActivity])

  return {
    session,
    status,
    refreshSession,
    checkSessionValidity,
    lastActivity: lastActivityRef.current
  }
}
