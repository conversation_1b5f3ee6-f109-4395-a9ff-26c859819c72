// 管理员侧边栏滚动修复脚本
(function() {
  'use strict';
  
  function fixSidebarScroll() {
    console.log('🔧 开始修复管理员侧边栏滚动...');
    
    // 查找侧边栏元素
    const nav = document.querySelector('nav.admin-sidebar-scroll');
    const container = nav?.parentElement;
    const sidebar = document.querySelector('.lg\\:w-64');
    
    if (!nav) {
      console.log('❌ 未找到侧边栏导航元素');
      return false;
    }
    
    console.log('✅ 找到侧边栏元素');
    console.log('导航元素:', nav);
    console.log('容器元素:', container);
    console.log('侧边栏元素:', sidebar);
    
    // 强制设置侧边栏高度
    if (sidebar) {
      sidebar.style.height = '100vh';
      console.log('✅ 设置侧边栏高度为100vh');
    }
    
    if (container) {
      container.style.cssText = `
        height: calc(100vh - 4rem) !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
      `;
      console.log('✅ 设置容器样式');
    }
    
    // 设置导航元素样式
    nav.style.cssText = `
      height: 100% !important;
      max-height: calc(100vh - 4rem) !important;
      overflow-y: auto !important;
      display: block !important;
      visibility: visible !important;
      flex: 1 !important;
    `;
    console.log('✅ 设置导航元素样式');
    
    // 强制重新计算布局
    nav.offsetHeight;
    if (container) container.offsetHeight;
    if (sidebar) sidebar.offsetHeight;
    
    // 检查修复结果
    setTimeout(() => {
      console.log('🔍 检查修复结果:');
      console.log('导航高度:', nav.offsetHeight, '滚动高度:', nav.scrollHeight, '客户端高度:', nav.clientHeight);
      console.log('容器高度:', container?.offsetHeight);
      console.log('侧边栏高度:', sidebar?.offsetHeight);
      
      const canScroll = nav.scrollHeight > nav.clientHeight;
      console.log('是否可滚动:', canScroll);
      
      if (canScroll && nav.clientHeight > 0) {
        console.log('🎉 侧边栏滚动修复成功！');
        
        // 添加滚动条样式
        const style = document.createElement('style');
        style.textContent = `
          .admin-sidebar-scroll::-webkit-scrollbar {
            width: 6px;
          }
          .admin-sidebar-scroll::-webkit-scrollbar-track {
            background: #f3f4f6;
            border-radius: 3px;
          }
          .admin-sidebar-scroll::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
            transition: background-color 0.2s ease;
          }
          .admin-sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
          }
        `;
        document.head.appendChild(style);
        console.log('✅ 添加滚动条样式');
        
        return true;
      } else {
        console.log('❌ 侧边栏滚动修复失败');
        return false;
      }
    }, 100);
    
    return true;
  }
  
  // 页面加载完成后执行修复
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixSidebarScroll);
  } else {
    fixSidebarScroll();
  }
  
  // 也在窗口加载完成后执行修复
  window.addEventListener('load', () => {
    setTimeout(fixSidebarScroll, 500);
  });
  
  // 监听路由变化（对于SPA应用）
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      if (url.includes('/admin')) {
        setTimeout(fixSidebarScroll, 1000);
      }
    }
  }).observe(document, { subtree: true, childList: true });
  
})();
