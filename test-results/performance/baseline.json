{"基线测试_综合": {"testName": "基线测试_综合", "metrics": [{"name": "基线测试_综合_iteration_1", "duration": 0.19970800000010058, "memoryUsage": {"before": {"rss": 1016889344, "heapTotal": 205357056, "heapUsed": 77508816, "external": 5550687, "arrayBuffers": 607068}, "after": {"rss": 1017102336, "heapTotal": 205357056, "heapUsed": 77872584, "external": 5550687, "arrayBuffers": 607068}, "delta": 363768}, "timestamp": 1754123931281, "metadata": {"iteration": 1}}, {"name": "基线测试_综合_iteration_2", "duration": 0.17575000000010732, "memoryUsage": {"before": {"rss": 1017102336, "heapTotal": 205357056, "heapUsed": 77876008, "external": 5550687, "arrayBuffers": 607068}, "after": {"rss": 1017298944, "heapTotal": 205357056, "heapUsed": 78242784, "external": 5550687, "arrayBuffers": 607068}, "delta": 366776}, "timestamp": 1754123931281, "metadata": {"iteration": 2}}], "summary": {"avgDuration": 0.18772900000010395, "minDuration": 0.17575000000010732, "maxDuration": 0.19970800000010058, "totalMemoryDelta": 730544, "iterations": 2}}}