{"numTotalTestSuites": 120, "numPassedTestSuites": 120, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 216, "numPassedTests": 216, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1754123930251, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够运行基本测试", "status": "passed", "title": "应该能够运行基本测试", "duration": 0.5395830000006754, "failureMessages": [], "location": {"line": 4, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试字符串", "status": "passed", "title": "应该能够测试字符串", "duration": 0.06479200000012497, "failureMessages": [], "location": {"line": 8, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试对象", "status": "passed", "title": "应该能够测试对象", "duration": 0.2240830000000642, "failureMessages": [], "location": {"line": 12, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试数组", "status": "passed", "title": "应该能够测试数组", "duration": 0.39379099999950995, "failureMessages": [], "location": {"line": 17, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试异步函数", "status": "passed", "title": "应该能够测试异步函数", "duration": 0.18458299999929295, "failureMessages": [], "location": {"line": 23, "column": 3}, "meta": {}}], "startTime": 1754123936202, "endTime": 1754123936203.3938, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/basic.test.ts"}, {"assertionResults": [{"ancestorTitles": ["托管服务测试", "区块链钱包验证"], "fullName": "托管服务测试 区块链钱包验证 应该验证有效的钱包地址格式", "status": "passed", "title": "应该验证有效的钱包地址格式", "duration": 0.9034169999999904, "failureMessages": [], "location": {"line": 368, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "区块链钱包验证"], "fullName": "托管服务测试 区块链钱包验证 应该生成正确的验证消息", "status": "passed", "title": "应该生成正确的验证消息", "duration": 0.23566699999992125, "failureMessages": [], "location": {"line": 376, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "区块链钱包验证"], "fullName": "托管服务测试 区块链钱包验证 应该验证钱包签名", "status": "passed", "title": "应该验证钱包签名", "duration": 0.13116600000012113, "failureMessages": [], "location": {"line": 387, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "风险评估系统"], "fullName": "托管服务测试 风险评估系统 应该正确评估低风险交易", "status": "passed", "title": "应该正确评估低风险交易", "duration": 0.1744579999999587, "failureMessages": [], "location": {"line": 403, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "风险评估系统"], "fullName": "托管服务测试 风险评估系统 应该正确评估高风险交易", "status": "passed", "title": "应该正确评估高风险交易", "duration": 0.1918749999999818, "failureMessages": [], "location": {"line": 416, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "风险评估系统"], "fullName": "托管服务测试 风险评估系统 应该检测新用户风险", "status": "passed", "title": "应该检测新用户风险", "duration": 0.13266699999985576, "failureMessages": [], "location": {"line": 429, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "托管订单管理"], "fullName": "托管服务测试 托管订单管理 应该创建托管订单", "status": "passed", "title": "应该创建托管订单", "duration": 0.1996249999997417, "failureMessages": [], "location": {"line": 470, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "托管订单管理"], "fullName": "托管服务测试 托管订单管理 应该更新托管订单状态", "status": "passed", "title": "应该更新托管订单状态", "duration": 0.2314169999999649, "failureMessages": [], "location": {"line": 480, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "争议仲裁系统"], "fullName": "托管服务测试 争议仲裁系统 应该创建争议记录", "status": "passed", "title": "应该创建争议记录", "duration": 0.15850000000000364, "failureMessages": [], "location": {"line": 506, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "争议仲裁系统"], "fullName": "托管服务测试 争议仲裁系统 应该记录仲裁投票", "status": "passed", "title": "应该记录仲裁投票", "duration": 0.1667080000001988, "failureMessages": [], "location": {"line": 516, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "激励机制"], "fullName": "托管服务测试 激励机制 应该发放投票参与奖励", "status": "passed", "title": "应该发放投票参与奖励", "duration": 0.1562090000002172, "failureMessages": [], "location": {"line": 553, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "激励机制"], "fullName": "托管服务测试 激励机制 应该发放成功调解奖励", "status": "passed", "title": "应该发放成功调解奖励", "duration": 0.09712499999977808, "failureMessages": [], "location": {"line": 573, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "激励机制"], "fullName": "托管服务测试 激励机制 应该正确统计奖励信息", "status": "passed", "title": "应该正确统计奖励信息", "duration": 0.10620799999969677, "failureMessages": [], "location": {"line": 591, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "激励机制"], "fullName": "托管服务测试 激励机制 应该检查月度奖励限制", "status": "passed", "title": "应该检查月度奖励限制", "duration": 0.10154200000033597, "failureMessages": [], "location": {"line": 613, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "区块链交易验证"], "fullName": "托管服务测试 区块链交易验证 应该验证 USDT 转账交易", "status": "passed", "title": "应该验证 USDT 转账交易", "duration": 0.5011250000002292, "failureMessages": [], "location": {"line": 636, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "数据完整性"], "fullName": "托管服务测试 数据完整性 应该维护托管订单和争议的关联关系", "status": "passed", "title": "应该维护托管订单和争议的关联关系", "duration": 0.23325000000022555, "failureMessages": [], "location": {"line": 659, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "数据完整性"], "fullName": "托管服务测试 数据完整性 应该维护用户和奖励的关联关系", "status": "passed", "title": "应该维护用户和奖励的关联关系", "duration": 0.1360420000000886, "failureMessages": [], "location": {"line": 672, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "错误处理"], "fullName": "托管服务测试 错误处理 应该处理无效的用户ID", "status": "passed", "title": "应该处理无效的用户ID", "duration": 0.0777499999999236, "failureMessages": [], "location": {"line": 697, "column": 5}, "meta": {}}, {"ancestorTitles": ["托管服务测试", "错误处理"], "fullName": "托管服务测试 错误处理 应该处理重复的奖励发放", "status": "passed", "title": "应该处理重复的奖励发放", "duration": 0.0830839999998716, "failureMessages": [], "location": {"line": 707, "column": 5}, "meta": {}}, {"ancestorTitles": ["API 端点测试"], "fullName": "API 端点测试 应该测试钱包验证 API", "status": "passed", "title": "应该测试钱包验证 API", "duration": 0.033625000000029104, "failureMessages": [], "location": {"line": 737, "column": 3}, "meta": {}}, {"ancestorTitles": ["API 端点测试"], "fullName": "API 端点测试 应该测试风险评估 API", "status": "passed", "title": "应该测试风险评估 API", "duration": 0.026917000000139524, "failureMessages": [], "location": {"line": 741, "column": 3}, "meta": {}}, {"ancestorTitles": ["API 端点测试"], "fullName": "API 端点测试 应该测试托管订单 API", "status": "passed", "title": "应该测试托管订单 API", "duration": 0.02795800000012605, "failureMessages": [], "location": {"line": 745, "column": 3}, "meta": {}}], "startTime": 1754123932863, "endTime": 1754123932867.2332, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/escrow-service.test.ts"}, {"assertionResults": [{"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should not render when user is not logged in", "status": "passed", "title": "should not render when user is not logged in", "duration": 5.5130410000001575, "failureMessages": [], "location": {"line": 25, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should render heart icon when user is logged in", "status": "passed", "title": "should render heart icon when user is logged in", "duration": 27.693084, "failureMessages": [], "location": {"line": 36, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should show solid heart when product is favorited", "status": "passed", "title": "should show solid heart when product is favorited", "duration": 10.323167000000012, "failureMessages": [], "location": {"line": 59, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should toggle favorite status when clicked", "status": "passed", "title": "should toggle favorite status when clicked", "duration": 6.591832999999951, "failureMessages": [], "location": {"line": 83, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "Favorite<PERSON><PERSON><PERSON> should show text when showText prop is true", "status": "passed", "title": "should show text when showText prop is true", "duration": 3.1359999999999673, "failureMessages": [], "location": {"line": 127, "column": 3}, "meta": {}}], "startTime": 1754123931758, "endTime": 1754123931811.136, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/app/__tests__/favorites.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["管理员功能测试", "管理员权限验证"], "fullName": "管理员功能测试 管理员权限验证 应该验证管理员身份", "status": "passed", "title": "应该验证管理员身份", "duration": 1.106499999999869, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "管理员权限验证"], "fullName": "管理员功能测试 管理员权限验证 应该拒绝非管理员用户访问管理功能", "status": "passed", "title": "应该拒绝非管理员用户访问管理功能", "duration": 0.19566699999995762, "failureMessages": [], "location": {"line": 45, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "商品审核功能"], "fullName": "管理员功能测试 商品审核功能 应该获取待审核商品列表", "status": "passed", "title": "应该获取待审核商品列表", "duration": 0.4881249999998545, "failureMessages": [], "location": {"line": 73, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "商品审核功能"], "fullName": "管理员功能测试 商品审核功能 应该能够审核通过商品", "status": "passed", "title": "应该能够审核通过商品", "duration": 0.17879100000027393, "failureMessages": [], "location": {"line": 102, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "商品审核功能"], "fullName": "管理员功能测试 商品审核功能 应该能够拒绝商品", "status": "passed", "title": "应该能够拒绝商品", "duration": 0.17945800000006784, "failureMessages": [], "location": {"line": 129, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "用户管理功能"], "fullName": "管理员功能测试 用户管理功能 应该获取用户列表", "status": "passed", "title": "应该获取用户列表", "duration": 0.2328749999996944, "failureMessages": [], "location": {"line": 164, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "用户管理功能"], "fullName": "管理员功能测试 用户管理功能 应该能够封禁用户", "status": "passed", "title": "应该能够封禁用户", "duration": 0.17829100000017206, "failureMessages": [], "location": {"line": 193, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "用户管理功能"], "fullName": "管理员功能测试 用户管理功能 应该能够解封用户", "status": "passed", "title": "应该能够解封用户", "duration": 0.1796249999997599, "failureMessages": [], "location": {"line": 226, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "用户管理功能"], "fullName": "管理员功能测试 用户管理功能 应该能够调整用户信用分", "status": "passed", "title": "应该能够调整用户信用分", "duration": 0.1946250000000873, "failureMessages": [], "location": {"line": 258, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "订单监控功能"], "fullName": "管理员功能测试 订单监控功能 应该获取所有订单列表", "status": "passed", "title": "应该获取所有订单列表", "duration": 0.2742910000001757, "failureMessages": [], "location": {"line": 294, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "订单监控功能"], "fullName": "管理员功能测试 订单监控功能 应该能够强制完成订单", "status": "passed", "title": "应该能够强制完成订单", "duration": 0.18983299999990777, "failureMessages": [], "location": {"line": 318, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "订单监控功能"], "fullName": "管理员功能测试 订单监控功能 应该能够处理退款申请", "status": "passed", "title": "应该能够处理退款申请", "duration": 0.17374999999992724, "failureMessages": [], "location": {"line": 347, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "申诉处理功能"], "fullName": "管理员功能测试 申诉处理功能 应该获取待处理申诉列表", "status": "passed", "title": "应该获取待处理申诉列表", "duration": 0.2082500000001346, "failureMessages": [], "location": {"line": 386, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "申诉处理功能"], "fullName": "管理员功能测试 申诉处理功能 应该能够处理申诉", "status": "passed", "title": "应该能够处理申诉", "duration": 0.18537500000002183, "failureMessages": [], "location": {"line": 430, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "系统配置管理"], "fullName": "管理员功能测试 系统配置管理 应该能够更新系统配置", "status": "passed", "title": "应该能够更新系统配置", "duration": 0.3182919999999285, "failureMessages": [], "location": {"line": 470, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "系统配置管理"], "fullName": "管理员功能测试 系统配置管理 应该能够获取系统统计数据", "status": "passed", "title": "应该能够获取系统统计数据", "duration": 0.2830420000000231, "failureMessages": [], "location": {"line": 498, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "管理员操作日志"], "fullName": "管理员功能测试 管理员操作日志 应该记录管理员操作", "status": "passed", "title": "应该记录管理员操作", "duration": 0.15120899999965332, "failureMessages": [], "location": {"line": 532, "column": 5}, "meta": {}}, {"ancestorTitles": ["管理员功能测试", "管理员操作日志"], "fullName": "管理员功能测试 管理员操作日志 应该获取操作日志列表", "status": "passed", "title": "应该获取操作日志列表", "duration": 0.21849999999994907, "failureMessages": [], "location": {"line": 560, "column": 5}, "meta": {}}], "startTime": 1754123932617, "endTime": 1754123932622.3184, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/e2e/admin-functions.test.ts"}, {"assertionResults": [{"ancestorTitles": ["完整业务流程测试", "商品上架到审核流程"], "fullName": "完整业务流程测试 商品上架到审核流程 应该完成商品上架到审核通过的完整流程", "status": "passed", "title": "应该完成商品上架到审核通过的完整流程", "duration": 1.4710839999997916, "failureMessages": [], "location": {"line": 20, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "商品上架到审核流程"], "fullName": "完整业务流程测试 商品上架到审核流程 应该处理商品审核拒绝流程", "status": "passed", "title": "应该处理商品审核拒绝流程", "duration": 0.19420799999988958, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "完整交易流程"], "fullName": "完整业务流程测试 完整交易流程 应该完成从下单到交易完成的完整流程", "status": "passed", "title": "应该完成从下单到交易完成的完整流程", "duration": 0.5451250000000982, "failureMessages": [], "location": {"line": 148, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "消息交流流程"], "fullName": "完整业务流程测试 消息交流流程 应该支持买卖双方完整的消息交流", "status": "passed", "title": "应该支持买卖双方完整的消息交流", "duration": 0.5276249999997162, "failureMessages": [], "location": {"line": 324, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "争议处理流程"], "fullName": "完整业务流程测试 争议处理流程 应该完成从申诉到仲裁的完整流程", "status": "passed", "title": "应该完成从申诉到仲裁的完整流程", "duration": 0.3592910000002121, "failureMessages": [], "location": {"line": 444, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "异常情况处理"], "fullName": "完整业务流程测试 异常情况处理 应该处理订单超时自动完成", "status": "passed", "title": "应该处理订单超时自动完成", "duration": 0.17000000000007276, "failureMessages": [], "location": {"line": 579, "column": 5}, "meta": {}}, {"ancestorTitles": ["完整业务流程测试", "异常情况处理"], "fullName": "完整业务流程测试 异常情况处理 应该处理支付超时取消订单", "status": "passed", "title": "应该处理支付超时取消订单", "duration": 0.16849999999976717, "failureMessages": [], "location": {"line": 606, "column": 5}, "meta": {}}], "startTime": 1754123933092, "endTime": 1754123933096.17, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/e2e/business-flow.test.ts"}, {"assertionResults": [{"ancestorTitles": ["用户功能端到端测试", "用户注册和登录流程"], "fullName": "用户功能端到端测试 用户注册和登录流程 应该完成完整的注册流程", "status": "passed", "title": "应该完成完整的注册流程", "duration": 1.2796250000001237, "failureMessages": [], "location": {"line": 33, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "用户注册和登录流程"], "fullName": "用户功能端到端测试 用户注册和登录流程 应该完成登录流程", "status": "passed", "title": "应该完成登录流程", "duration": 0.8127919999997175, "failureMessages": [], "location": {"line": 75, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "商品浏览和搜索流程"], "fullName": "用户功能端到端测试 商品浏览和搜索流程 应该能够浏览商品列表", "status": "passed", "title": "应该能够浏览商品列表", "duration": 0.3969999999999345, "failureMessages": [], "location": {"line": 104, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "商品浏览和搜索流程"], "fullName": "用户功能端到端测试 商品浏览和搜索流程 应该能够搜索商品", "status": "passed", "title": "应该能够搜索商品", "duration": 0.25287500000013097, "failureMessages": [], "location": {"line": 133, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "商品浏览和搜索流程"], "fullName": "用户功能端到端测试 商品浏览和搜索流程 应该能够查看商品详情", "status": "passed", "title": "应该能够查看商品详情", "duration": 0.2070410000001175, "failureMessages": [], "location": {"line": 160, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "下单和支付流程"], "fullName": "用户功能端到端测试 下单和支付流程 应该能够创建订单", "status": "passed", "title": "应该能够创建订单", "duration": 0.7293749999998909, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "下单和支付流程"], "fullName": "用户功能端到端测试 下单和支付流程 应该能够获取支付信息", "status": "passed", "title": "应该能够获取支付信息", "duration": 0.4155839999998534, "failureMessages": [], "location": {"line": 240, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "下单和支付流程"], "fullName": "用户功能端到端测试 下单和支付流程 应该能够提交支付信息", "status": "passed", "title": "应该能够提交支付信息", "duration": 0.2357080000001588, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "消息交流流程"], "fullName": "用户功能端到端测试 消息交流流程 应该能够发送文本消息", "status": "passed", "title": "应该能够发送文本消息", "duration": 1.3560410000000047, "failureMessages": [], "location": {"line": 309, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "消息交流流程"], "fullName": "用户功能端到端测试 消息交流流程 应该能够获取消息历史", "status": "passed", "title": "应该能够获取消息历史", "duration": 0.5909169999999904, "failureMessages": [], "location": {"line": 344, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "订单状态更新流程"], "fullName": "用户功能端到端测试 订单状态更新流程 应该能够确认收货", "status": "passed", "title": "应该能够确认收货", "duration": 0.43025000000034197, "failureMessages": [], "location": {"line": 399, "column": 5}, "meta": {}}, {"ancestorTitles": ["用户功能端到端测试", "完整用户流程集成测试"], "fullName": "用户功能端到端测试 完整用户流程集成测试 应该完成从注册到交易完成的完整流程", "status": "passed", "title": "应该完成从注册到交易完成的完整流程", "duration": 0.7004159999996773, "failureMessages": [], "location": {"line": 429, "column": 5}, "meta": {}}], "startTime": 1754123932745, "endTime": 1754123932752.7004, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/e2e/user-flow.test.ts"}, {"assertionResults": [{"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该成功注册新用户", "status": "passed", "title": "应该成功注册新用户", "duration": 7.4988750000002256, "failureMessages": [], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该拒绝重复邮箱注册", "status": "passed", "title": "应该拒绝重复邮箱注册", "duration": 2.866375000000062, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该验证必填字段", "status": "passed", "title": "应该验证必填字段", "duration": 1.0947919999998703, "failureMessages": [], "location": {"line": 137, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该处理数据库错误", "status": "passed", "title": "应该处理数据库错误", "duration": 1.9614160000000993, "failureMessages": [], "location": {"line": 152, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该为已登录用户生成Socket令牌", "status": "passed", "title": "应该为已登录用户生成Socket令牌", "duration": 0.6390409999999065, "failureMessages": [], "location": {"line": 177, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该拒绝未登录用户", "status": "passed", "title": "应该拒绝未登录用户", "duration": 0.3397909999998774, "failureMessages": [], "location": {"line": 217, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该处理JWT签名错误", "status": "passed", "title": "应该处理JWT签名错误", "duration": 0.815541999999823, "failureMessages": [], "location": {"line": 235, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "认证中间件测试"], "fullName": "认证API测试 认证中间件测试 应该验证有效的JWT令牌", "status": "passed", "title": "应该验证有效的JWT令牌", "duration": 0.13999999999987267, "failureMessages": [], "location": {"line": 268, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "认证中间件测试"], "fullName": "认证API测试 认证中间件测试 应该拒绝无效的JWT令牌", "status": "passed", "title": "应该拒绝无效的JWT令牌", "duration": 0.36524999999983265, "failureMessages": [], "location": {"line": 284, "column": 5}, "meta": {}}], "startTime": 1754123932129, "endTime": 1754123932144.3652, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/auth.test.ts"}, {"assertionResults": [{"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该返回订单相关的消息", "status": "passed", "title": "应该返回订单相关的消息", "duration": 5.186249999999745, "failureMessages": [], "location": {"line": 45, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该拒绝无关用户访问消息", "status": "passed", "title": "应该拒绝无关用户访问消息", "duration": 1.0932499999998981, "failureMessages": [], "location": {"line": 107, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该处理订单不存在的情况", "status": "passed", "title": "应该处理订单不存在的情况", "duration": 0.7361670000000231, "failureMessages": [], "location": {"line": 128, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该成功发送文本消息", "status": "passed", "title": "应该成功发送文本消息", "duration": 0.8197909999998956, "failureMessages": [], "location": {"line": 145, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该成功发送图片消息", "status": "passed", "title": "应该成功发送图片消息", "duration": 0.5776250000003529, "failureMessages": [], "location": {"line": 217, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该验证文本消息的内容", "status": "passed", "title": "应该验证文本消息的内容", "duration": 0.4212089999996351, "failureMessages": [], "location": {"line": 267, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该验证文件消息的文件URL", "status": "passed", "title": "应该验证文件消息的文件URL", "duration": 0.3547079999998459, "failureMessages": [], "location": {"line": 286, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该拒绝未登录用户发送消息", "status": "passed", "title": "应该拒绝未登录用户发送消息", "duration": 0.2655410000002121, "failureMessages": [], "location": {"line": 306, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该拒绝无关用户发送消息", "status": "passed", "title": "应该拒绝无关用户发送消息", "duration": 0.3083750000000691, "failureMessages": [], "location": {"line": 324, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该处理数据库错误", "status": "passed", "title": "应该处理数据库错误", "duration": 1.0637079999996786, "failureMessages": [], "location": {"line": 351, "column": 5}, "meta": {}}], "startTime": 1754123932286, "endTime": 1754123932297.0637, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/messages.test.ts"}, {"assertionResults": [{"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该成功创建订单", "status": "passed", "title": "应该成功创建订单", "duration": 4.638249999999971, "failureMessages": [], "location": {"line": 125, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该拒绝购买自己的商品", "status": "passed", "title": "应该拒绝购买自己的商品", "duration": 1.3785409999998137, "failureMessages": [], "location": {"line": 197, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该检查库存不足", "status": "passed", "title": "应该检查库存不足", "duration": 1.7478329999999005, "failureMessages": [], "location": {"line": 231, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id] - 获取订单详情"], "fullName": "订单API测试 GET /api/orders/[id] - 获取订单详情 应该返回买家的订单详情", "status": "passed", "title": "应该返回买家的订单详情", "duration": 0.8272090000000389, "failureMessages": [], "location": {"line": 268, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id] - 获取订单详情"], "fullName": "订单API测试 GET /api/orders/[id] - 获取订单详情 应该拒绝无关用户访问订单", "status": "passed", "title": "应该拒绝无关用户访问订单", "duration": 0.48083299999984774, "failureMessages": [], "location": {"line": 297, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "PATCH /api/orders/[id] - 更新订单状态"], "fullName": "订单API测试 PATCH /api/orders/[id] - 更新订单状态 应该允许买家上传支付凭证", "status": "passed", "title": "应该允许买家上传支付凭证", "duration": 2.314791000000014, "failureMessages": [], "location": {"line": 323, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "PATCH /api/orders/[id] - 更新订单状态"], "fullName": "订单API测试 PATCH /api/orders/[id] - 更新订单状态 应该允许卖家确认支付", "status": "passed", "title": "应该允许卖家确认支付", "duration": 0.6925839999998971, "failureMessages": [], "location": {"line": 362, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders/[id]/payment - 处理支付"], "fullName": "订单API测试 POST /api/orders/[id]/payment - 处理支付 应该处理币安支付", "status": "passed", "title": "应该处理币安支付", "duration": 1.1604999999999563, "failureMessages": [], "location": {"line": 399, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders/[id]/payment - 处理支付"], "fullName": "订单API测试 POST /api/orders/[id]/payment - 处理支付 应该处理BNB链支付", "status": "passed", "title": "应该处理BNB链支付", "duration": 0.6329170000001341, "failureMessages": [], "location": {"line": 440, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id]/payment - 获取支付信息"], "fullName": "订单API测试 GET /api/orders/[id]/payment - 获取支付信息 应该为买家生成支付二维码", "status": "passed", "title": "应该为买家生成支付二维码", "duration": 0.5132080000000769, "failureMessages": [], "location": {"line": 481, "column": 5}, "meta": {}}], "startTime": 1754123931944, "endTime": 1754123931958.5132, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/orders.test.ts"}, {"assertionResults": [{"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该返回已审核通过的商品列表", "status": "passed", "title": "应该返回已审核通过的商品列表", "duration": 5.612875000000031, "failureMessages": [], "location": {"line": 119, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持搜索功能", "status": "passed", "title": "应该支持搜索功能", "duration": 1.1900829999999587, "failureMessages": [], "location": {"line": 155, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持分类筛选", "status": "passed", "title": "应该支持分类筛选", "duration": 0.6945419999999558, "failureMessages": [], "location": {"line": 179, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持价格范围筛选", "status": "passed", "title": "应该支持价格范围筛选", "duration": 0.5961659999998119, "failureMessages": [], "location": {"line": 203, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该成功创建商品", "status": "passed", "title": "应该成功创建商品", "duration": 0.9117909999999938, "failureMessages": [], "location": {"line": 231, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该拒绝未登录用户创建商品", "status": "passed", "title": "应该拒绝未登录用户创建商品", "duration": 0.3899580000002061, "failureMessages": [], "location": {"line": 292, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该验证必填字段", "status": "passed", "title": "应该验证必填字段", "duration": 0.3952920000001541, "failureMessages": [], "location": {"line": 309, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products/[id] - 获取商品详情"], "fullName": "商品API测试 GET /api/products/[id] - 获取商品详情 应该返回商品详情", "status": "passed", "title": "应该返回商品详情", "duration": 0.5410000000001673, "failureMessages": [], "location": {"line": 330, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products/[id] - 获取商品详情"], "fullName": "商品API测试 GET /api/products/[id] - 获取商品详情 应该处理商品不存在的情况", "status": "passed", "title": "应该处理商品不存在的情况", "duration": 0.9844580000001315, "failureMessages": [], "location": {"line": 353, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "PATCH /api/products/[id] - 更新商品"], "fullName": "商品API测试 PATCH /api/products/[id] - 更新商品 应该允许卖家更新自己的商品", "status": "passed", "title": "应该允许卖家更新自己的商品", "duration": 1.0550410000000738, "failureMessages": [], "location": {"line": 369, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "PATCH /api/products/[id] - 更新商品"], "fullName": "商品API测试 PATCH /api/products/[id] - 更新商品 应该拒绝非卖家更新商品", "status": "passed", "title": "应该拒绝非卖家更新商品", "duration": 0.7480840000000626, "failureMessages": [], "location": {"line": 429, "column": 5}, "meta": {}}], "startTime": 1754123931979, "endTime": 1754123931992.748, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/products.test.ts"}, {"assertionResults": [{"ancestorTitles": ["简化API测试", "Mock功能测试"], "fullName": "简化API测试 Mock功能测试 应该能够创建模拟请求", "status": "passed", "title": "应该能够创建模拟请求", "duration": 2.6616659999999683, "failureMessages": [], "location": {"line": 17, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "Mock功能测试"], "fullName": "简化API测试 Mock功能测试 应该能够创建模拟会话", "status": "passed", "title": "应该能够创建模拟会话", "duration": 0.2474170000000413, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "Mock功能测试"], "fullName": "简化API测试 Mock功能测试 应该能够模拟数据库操作", "status": "passed", "title": "应该能够模拟数据库操作", "duration": 0.7751659999998992, "failureMessages": [], "location": {"line": 29, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "测试数据验证"], "fullName": "简化API测试 测试数据验证 应该有有效的用户测试数据", "status": "passed", "title": "应该有有效的用户测试数据", "duration": 0.23474999999984902, "failureMessages": [], "location": {"line": 44, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "测试数据验证"], "fullName": "简化API测试 测试数据验证 应该有有效的商品测试数据", "status": "passed", "title": "应该有有效的商品测试数据", "duration": 0.19116600000006656, "failureMessages": [], "location": {"line": 58, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "测试数据验证"], "fullName": "简化API测试 测试数据验证 应该有有效的订单测试数据", "status": "passed", "title": "应该有有效的订单测试数据", "duration": 0.6270830000000842, "failureMessages": [], "location": {"line": 69, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "HTTP响应模拟"], "fullName": "简化API测试 HTTP响应模拟 应该能够模拟成功的API响应", "status": "passed", "title": "应该能够模拟成功的API响应", "duration": 0.41508299999986775, "failureMessages": [], "location": {"line": 81, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "HTTP响应模拟"], "fullName": "简化API测试 HTTP响应模拟 应该能够模拟错误的API响应", "status": "passed", "title": "应该能够模拟错误的API响应", "duration": 0.22483299999998962, "failureMessages": [], "location": {"line": 100, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "异步操作测试"], "fullName": "简化API测试 异步操作测试 应该能够处理Promise", "status": "passed", "title": "应该能够处理Promise", "duration": 0.12349999999992178, "failureMessages": [], "location": {"line": 119, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "异步操作测试"], "fullName": "简化API测试 异步操作测试 应该能够处理Promise拒绝", "status": "passed", "title": "应该能够处理Promise拒绝", "duration": 0.8047500000000127, "failureMessages": [], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "异步操作测试"], "fullName": "简化API测试 异步操作测试 应该能够测试超时", "status": "passed", "title": "应该能够测试超时", "duration": 99.95616700000005, "failureMessages": [], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "数据验证测试"], "fullName": "简化API测试 数据验证测试 应该验证邮箱格式", "status": "passed", "title": "应该验证邮箱格式", "duration": 0.31475000000000364, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "数据验证测试"], "fullName": "简化API测试 数据验证测试 应该验证价格范围", "status": "passed", "title": "应该验证价格范围", "duration": 0.1435839999999189, "failureMessages": [], "location": {"line": 153, "column": 5}, "meta": {}}, {"ancestorTitles": ["简化API测试", "数据验证测试"], "fullName": "简化API测试 数据验证测试 应该验证字符串长度", "status": "passed", "title": "应该验证字符串长度", "duration": 0.11537499999985812, "failureMessages": [], "location": {"line": 163, "column": 5}, "meta": {}}], "startTime": 1754123931569, "endTime": 1754123931676.1436, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/simple-api.test.ts"}, {"assertionResults": [{"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该成功上传商品图片", "status": "passed", "title": "应该成功上传商品图片", "duration": 3.6412500000001273, "failureMessages": [], "location": {"line": 57, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该成功上传用户头像", "status": "passed", "title": "应该成功上传用户头像", "duration": 0.9863749999999527, "failureMessages": [], "location": {"line": 90, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该成功上传支付凭证", "status": "passed", "title": "应该成功上传支付凭证", "duration": 0.4928340000001299, "failureMessages": [], "location": {"line": 122, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该拒绝未登录用户上传", "status": "passed", "title": "应该拒绝未登录用户上传", "duration": 1.0044590000002245, "failureMessages": [], "location": {"line": 154, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该拒绝无文件上传", "status": "passed", "title": "应该拒绝无文件上传", "duration": 0.37008300000024974, "failureMessages": [], "location": {"line": 176, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该拒绝不支持的文件类型", "status": "passed", "title": "应该拒绝不支持的文件类型", "duration": 0.3524999999999636, "failureMessages": [], "location": {"line": 195, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该拒绝过大的文件", "status": "passed", "title": "应该拒绝过大的文件", "duration": 0.36475000000018554, "failureMessages": [], "location": {"line": 214, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该处理图片处理错误", "status": "passed", "title": "应该处理图片处理错误", "duration": 0.99350000000004, "failureMessages": [], "location": {"line": 234, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该处理文件系统错误", "status": "passed", "title": "应该处理文件系统错误", "duration": 0.6447080000002643, "failureMessages": [], "location": {"line": 262, "column": 5}, "meta": {}}, {"ancestorTitles": ["文件上传API测试", "POST /api/upload - 文件上传"], "fullName": "文件上传API测试 POST /api/upload - 文件上传 应该生成唯一的文件名", "status": "passed", "title": "应该生成唯一的文件名", "duration": 0.843166999999994, "failureMessages": [], "location": {"line": 286, "column": 5}, "meta": {}}], "startTime": 1754123932315, "endTime": 1754123932324.8433, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/upload.test.ts"}, {"assertionResults": [{"ancestorTitles": ["性能和并发测试", "数据库并发访问测试"], "fullName": "性能和并发测试 数据库并发访问测试 应该处理大量并发商品查询", "status": "passed", "title": "应该处理大量并发商品查询", "duration": 2.972666000000004, "failureMessages": [], "location": {"line": 20, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "数据库并发访问测试"], "fullName": "性能和并发测试 数据库并发访问测试 应该处理并发订单创建", "status": "passed", "title": "应该处理并发订单创建", "duration": 0.7967919999999822, "failureMessages": [], "location": {"line": 68, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "数据库并发访问测试"], "fullName": "性能和并发测试 数据库并发访问测试 应该处理并发消息发送", "status": "passed", "title": "应该处理并发消息发送", "duration": 0.35037499999998545, "failureMessages": [], "location": {"line": 131, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "缓存性能测试"], "fullName": "性能和并发测试 缓存性能测试 应该测试缓存命中率", "status": "passed", "title": "应该测试缓存命中率", "duration": 0.6462500000000091, "failureMessages": [], "location": {"line": 183, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "缓存性能测试"], "fullName": "性能和并发测试 缓存性能测试 应该测试缓存失效和更新", "status": "passed", "title": "应该测试缓存失效和更新", "duration": 0.3504169999999931, "failureMessages": [], "location": {"line": 236, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "WebSocket连接测试"], "fullName": "性能和并发测试 WebSocket连接测试 应该处理大量并发WebSocket连接", "status": "passed", "title": "应该处理大量并发WebSocket连接", "duration": 98.07650000000001, "failureMessages": [], "location": {"line": 289, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "WebSocket连接测试"], "fullName": "性能和并发测试 WebSocket连接测试 应该测试WebSocket消息广播性能", "status": "passed", "title": "应该测试WebSocket消息广播性能", "duration": 0.8999999999999773, "failureMessages": [], "location": {"line": 336, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "内存和资源使用测试"], "fullName": "性能和并发测试 内存和资源使用测试 应该测试大量数据处理的内存使用", "status": "passed", "title": "应该测试大量数据处理的内存使用", "duration": 1.24737499999992, "failureMessages": [], "location": {"line": 380, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "内存和资源使用测试"], "fullName": "性能和并发测试 内存和资源使用测试 应该测试文件上传的并发处理", "status": "passed", "title": "应该测试文件上传的并发处理", "duration": 2.4869579999999587, "failureMessages": [], "location": {"line": 433, "column": 5}, "meta": {}}, {"ancestorTitles": ["性能和并发测试", "压力测试"], "fullName": "性能和并发测试 压力测试 应该进行系统整体压力测试", "status": "passed", "title": "应该进行系统整体压力测试", "duration": 5046.517541, "failureMessages": [], "location": {"line": 482, "column": 5}, "meta": {}}], "startTime": 1754123930806, "endTime": 1754123935960.5176, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/concurrent.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🧠 内存优化测试套件", "📊 内存优化数据工厂测试"], "fullName": "🧠 内存优化测试套件 📊 内存优化数据工厂测试 应该显著减少内存使用", "status": "passed", "title": "应该显著减少内存使用", "duration": 5.463958000000048, "failureMessages": [], "location": {"line": 60, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "📊 内存优化数据工厂测试"], "fullName": "🧠 内存优化测试套件 📊 内存优化数据工厂测试 应该有效使用对象池", "status": "passed", "title": "应该有效使用对象池", "duration": 0.71875, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🌊 流式数据生成测试"], "fullName": "🧠 内存优化测试套件 🌊 流式数据生成测试 应该支持大规模流式数据生成", "status": "passed", "title": "应该支持大规模流式数据生成", "duration": 8.041166999999973, "failureMessages": [], "location": {"line": 136, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🌊 流式数据生成测试"], "fullName": "🧠 内存优化测试套件 🌊 流式数据生成测试 应该支持异步批量处理", "status": "passed", "title": "应该支持异步批量处理", "duration": 28.725667000000044, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🔍 内存监控数据生成测试"], "fullName": "🧠 内存优化测试套件 🔍 内存监控数据生成测试 应该在内存监控下安全生成数据", "status": "passed", "title": "应该在内存监控下安全生成数据", "duration": 6.782292000000098, "failureMessages": [], "location": {"line": 221, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "📈 内存性能对比测试"], "fullName": "🧠 内存优化测试套件 📈 内存性能对比测试 应该比标准工厂更节省内存", "status": "passed", "title": "应该比标准工厂更节省内存", "duration": 3.0985829999999623, "failureMessages": [], "location": {"line": 250, "column": 5}, "meta": {}}], "startTime": 1754123930770, "endTime": 1754123930824.0986, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/memory-optimization.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该显著提升大批量用户数据生成速度", "status": "passed", "title": "应该显著提升大批量用户数据生成速度", "duration": 26.67999999999995, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该优化商品数据生成内存使用", "status": "passed", "title": "应该优化商品数据生成内存使用", "duration": 9.753917000000001, "failureMessages": [], "location": {"line": 36, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该支持高效的批量数据生成", "status": "passed", "title": "应该支持高效的批量数据生成", "duration": 13.056333999999993, "failureMessages": [], "location": {"line": 48, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "⚡ 并行处理性能测试"], "fullName": "🚀 优化后的性能测试套件 ⚡ 并行处理性能测试 应该支持高效的并行任务执行", "status": "passed", "title": "应该支持高效的并行任务执行", "duration": 10.019542000000001, "failureMessages": [], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "⚡ 并行处理性能测试"], "fullName": "🚀 优化后的性能测试套件 ⚡ 并行处理性能测试 应该优化API模拟的并发性能", "status": "passed", "title": "应该优化API模拟的并发性能", "duration": 9.163124999999923, "failureMessages": [], "location": {"line": 89, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🗄️ 数据库查询优化测试"], "fullName": "🚀 优化后的性能测试套件 🗄️ 数据库查询优化测试 应该优化商品搜索查询性能", "status": "passed", "title": "应该优化商品搜索查询性能", "duration": 113.20895799999994, "failureMessages": [], "location": {"line": 111, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🗄️ 数据库查询优化测试"], "fullName": "🚀 优化后的性能测试套件 🗄️ 数据库查询优化测试 应该提供查询性能分析和优化建议", "status": "passed", "title": "应该提供查询性能分析和优化建议", "duration": 119.35733299999993, "failureMessages": [], "location": {"line": 128, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "💾 缓存系统性能测试"], "fullName": "🚀 优化后的性能测试套件 💾 缓存系统性能测试 应该实现高性能LRU缓存", "status": "passed", "title": "应该实现高性能LRU缓存", "duration": 41.29762500000015, "failureMessages": [], "location": {"line": 144, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "💾 缓存系统性能测试"], "fullName": "🚀 优化后的性能测试套件 💾 缓存系统性能测试 应该实现高效的多级缓存", "status": "passed", "title": "应该实现高效的多级缓存", "duration": 33.716042000000016, "failureMessages": [], "location": {"line": 168, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🎯 综合性能基准测试"], "fullName": "🚀 优化后的性能测试套件 🎯 综合性能基准测试 应该达到系统性能目标", "status": "passed", "title": "应该达到系统性能目标", "duration": 27.320375000000013, "failureMessages": [], "location": {"line": 203, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🎯 综合性能基准测试"], "fullName": "🚀 优化后的性能测试套件 🎯 综合性能基准测试 应该检测性能回归", "status": "passed", "title": "应该检测性能回归", "duration": 3.628541000000041, "failureMessages": [], "location": {"line": 252, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📈 性能监控和报告"], "fullName": "🚀 优化后的性能测试套件 📈 性能监控和报告 应该生成详细的性能报告", "status": "passed", "title": "应该生成详细的性能报告", "duration": 1.403208000000177, "failureMessages": [], "location": {"line": 272, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📈 性能监控和报告"], "fullName": "🚀 优化后的性能测试套件 📈 性能监控和报告 应该保存性能基线数据", "status": "passed", "title": "应该保存性能基线数据", "duration": 0.9532079999999041, "failureMessages": [], "location": {"line": 295, "column": 5}, "meta": {}}], "startTime": 1754123930871, "endTime": 1754123931280.9531, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/optimized-performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量用户数据", "status": "passed", "title": "应该快速生成大量用户数据", "duration": 26.4140000000001, "failureMessages": [], "location": {"line": 6, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量商品数据", "status": "passed", "title": "应该快速生成大量商品数据", "duration": 9.731415999999967, "failureMessages": [], "location": {"line": 20, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量订单数据", "status": "passed", "title": "应该快速生成大量订单数据", "duration": 5.831083000000035, "failureMessages": [], "location": {"line": 34, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "并发操作模拟"], "fullName": "简单性能测试 并发操作模拟 应该处理并发数据库查询模拟", "status": "passed", "title": "应该处理并发数据库查询模拟", "duration": 11.131083999999987, "failureMessages": [], "location": {"line": 50, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "并发操作模拟"], "fullName": "简单性能测试 并发操作模拟 应该处理批量API请求模拟", "status": "passed", "title": "应该处理批量API请求模拟", "duration": 0.37370800000007875, "failureMessages": [], "location": {"line": 71, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "内存使用测试"], "fullName": "简单性能测试 内存使用测试 应该有效管理大量数据的内存使用", "status": "passed", "title": "应该有效管理大量数据的内存使用", "duration": 55.778458, "failureMessages": [], "location": {"line": 94, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速搜索大量数据", "status": "passed", "title": "应该快速搜索大量数据", "duration": 126.87187500000005, "failureMessages": [], "location": {"line": 122, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速排序大量数据", "status": "passed", "title": "应该快速排序大量数据", "duration": 75.53104200000007, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速分页处理大量数据", "status": "passed", "title": "应该快速分页处理大量数据", "duration": 131.67437500000005, "failureMessages": [], "location": {"line": 165, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "缓存性能模拟"], "fullName": "简单性能测试 缓存性能模拟 应该模拟缓存命中性能", "status": "passed", "title": "应该模拟缓存命中性能", "duration": 0.20754200000010314, "failureMessages": [], "location": {"line": 188, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "缓存性能模拟"], "fullName": "简单性能测试 缓存性能模拟 应该模拟缓存未命中性能", "status": "passed", "title": "应该模拟缓存未命中性能", "duration": 0.24179100000014841, "failureMessages": [], "location": {"line": 211, "column": 5}, "meta": {}}], "startTime": 1754123930841, "endTime": 1754123931285.2417, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/simple-performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📊 性能基准对比测试"], "fullName": "🚀 用户数据生成性能优化测试 📊 性能基准对比测试 应该显著提升10,000用户生成性能", "status": "passed", "title": "应该显著提升10,000用户生成性能", "duration": 83.13033399999995, "failureMessages": ["AssertionError: expected -68.96626484336885 to be greater than 30\n    at /Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/user-generation-optimization.test.ts:93:42\n    at file:///Users/<USER>/Desktop/bitmarket-v1.3.0/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Users/<USER>/Desktop/bitmarket-v1.3.0/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Users/<USER>/Desktop/bitmarket-v1.3.0/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Users/<USER>/Desktop/bitmarket-v1.3.0/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///Users/<USER>/Desktop/bitmarket-v1.3.0/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///Users/<USER>/Desktop/bitmarket-v1.3.0/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Users/<USER>/Desktop/bitmarket-v1.3.0/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///Users/<USER>/Desktop/bitmarket-v1.3.0/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)"], "location": {"line": 14, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📊 性能基准对比测试"], "fullName": "🚀 用户数据生成性能优化测试 📊 性能基准对比测试 应该在并行模式下实现更高性能", "status": "passed", "title": "应该在并行模式下实现更高性能", "duration": 78.96299999999997, "failureMessages": [], "location": {"line": 98, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🔍 内存使用优化测试"], "fullName": "🚀 用户数据生成性能优化测试 🔍 内存使用优化测试 应该优化大批量生成的内存使用", "status": "passed", "title": "应该优化大批量生成的内存使用", "duration": 9.482792000000018, "failureMessages": [], "location": {"line": 134, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🔍 内存使用优化测试"], "fullName": "🚀 用户数据生成性能优化测试 🔍 内存使用优化测试 应该验证内存池的效果", "status": "passed", "title": "应该验证内存池的效果", "duration": 3.244416999999885, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🎯 数据质量验证"], "fullName": "🚀 用户数据生成性能优化测试 🎯 数据质量验证 应该生成高质量的用户数据", "status": "passed", "title": "应该生成高质量的用户数据", "duration": 16.305958000000146, "failureMessages": [], "location": {"line": 190, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🎯 数据质量验证"], "fullName": "🚀 用户数据生成性能优化测试 🎯 数据质量验证 应该支持专门的买家和卖家生成", "status": "passed", "title": "应该支持专门的买家和卖家生成", "duration": 2.242708999999877, "failureMessages": [], "location": {"line": 221, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📈 极限性能测试"], "fullName": "🚀 用户数据生成性能优化测试 📈 极限性能测试 应该在极限条件下保持高性能", "status": "passed", "title": "应该在极限条件下保持高性能", "duration": 185.44429200000013, "failureMessages": [], "location": {"line": 244, "column": 5}, "meta": {}}], "startTime": 1754123931155, "endTime": 1754123931534.4443, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/performance/user-generation-optimization.test.ts"}, {"assertionResults": [{"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建有效的用户数据", "status": "passed", "title": "应该创建有效的用户数据", "duration": 2.1659159999999247, "failureMessages": [], "location": {"line": 14, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建买家用户", "status": "passed", "title": "应该创建买家用户", "duration": 0.4051249999997708, "failureMessages": [], "location": {"line": 28, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建卖家用户", "status": "passed", "title": "应该创建卖家用户", "duration": 0.328125, "failureMessages": [], "location": {"line": 35, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建管理员用户", "status": "passed", "title": "应该创建管理员用户", "duration": 0.26358299999992596, "failureMessages": [], "location": {"line": 43, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该批量创建用户", "status": "passed", "title": "应该批量创建用户", "duration": 0.8689159999998992, "failureMessages": [], "location": {"line": 51, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建有效的商品数据", "status": "passed", "title": "应该创建有效的商品数据", "duration": 0.3834590000001299, "failureMessages": [], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建可用商品", "status": "passed", "title": "应该创建可用商品", "duration": 0.14008400000011534, "failureMessages": [], "location": {"line": 76, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建待审核商品", "status": "passed", "title": "应该创建待审核商品", "duration": 0.15124999999989086, "failureMessages": [], "location": {"line": 84, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建有效的订单数据", "status": "passed", "title": "应该创建有效的订单数据", "duration": 0.426792000000205, "failureMessages": [], "location": {"line": 92, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建待支付订单", "status": "passed", "title": "应该创建待支付订单", "duration": 0.16329099999984464, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建已支付订单", "status": "passed", "title": "应该创建已支付订单", "duration": 0.14479099999971368, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建已完成订单", "status": "passed", "title": "应该创建已完成订单", "duration": 0.14495800000031522, "failureMessages": [], "location": {"line": 124, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建有效的消息数据", "status": "passed", "title": "应该创建有效的消息数据", "duration": 0.29654100000016115, "failureMessages": [], "location": {"line": 136, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建文本消息", "status": "passed", "title": "应该创建文本消息", "duration": 0.10958399999981339, "failureMessages": [], "location": {"line": 147, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建图片消息", "status": "passed", "title": "应该创建图片消息", "duration": 0.10812499999974534, "failureMessages": [], "location": {"line": 155, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建有效的评价数据", "status": "passed", "title": "应该创建有效的评价数据", "duration": 0.14770799999996598, "failureMessages": [], "location": {"line": 170, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建正面评价", "status": "passed", "title": "应该创建正面评价", "duration": 0.07845899999983885, "failureMessages": [], "location": {"line": 180, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建负面评价", "status": "passed", "title": "应该创建负面评价", "duration": 0.06895900000017718, "failureMessages": [], "location": {"line": 187, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建有效的反馈数据", "status": "passed", "title": "应该创建有效的反馈数据", "duration": 0.36720900000000256, "failureMessages": [], "location": {"line": 196, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建申诉反馈", "status": "passed", "title": "应该创建申诉反馈", "duration": 0.21533300000010058, "failureMessages": [], "location": {"line": 208, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建Bug报告", "status": "passed", "title": "应该创建Bug报告", "duration": 0.1187089999998534, "failureMessages": [], "location": {"line": 215, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "DataGenerator"], "fullName": "数据工厂测试 DataGenerator 应该生成完整的用户场景", "status": "passed", "title": "应该生成完整的用户场景", "duration": 1.0825420000001031, "failureMessages": [], "location": {"line": 224, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "DataGenerator"], "fullName": "数据工厂测试 DataGenerator 应该生成市场数据", "status": "passed", "title": "应该生成市场数据", "duration": 1.8882910000002084, "failureMessages": [], "location": {"line": 236, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成唯一的ID", "status": "passed", "title": "应该生成唯一的ID", "duration": 0.43837500000017826, "failureMessages": [], "location": {"line": 246, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成有效的邮箱地址", "status": "passed", "title": "应该生成有效的邮箱地址", "duration": 0.24149999999963256, "failureMessages": [], "location": {"line": 254, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成合理的价格范围", "status": "passed", "title": "应该生成合理的价格范围", "duration": 0.3882080000003043, "failureMessages": [], "location": {"line": 263, "column": 5}, "meta": {}}], "startTime": 1754123932455, "endTime": 1754123932466.4385, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/factories/factory.test.ts"}, {"assertionResults": [{"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡批次管理API"], "fullName": "增强礼品卡管理API测试 礼品卡批次管理API 应该成功批量生成礼品卡", "status": "passed", "title": "应该成功批量生成礼品卡", "duration": 3.1115410000002157, "failureMessages": [], "location": {"line": 57, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡批次管理API"], "fullName": "增强礼品卡管理API测试 礼品卡批次管理API 应该拒绝非管理员用户批量生成礼品卡", "status": "passed", "title": "应该拒绝非管理员用户批量生成礼品卡", "duration": 0.4070419999998194, "failureMessages": [], "location": {"line": 99, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡批次管理API"], "fullName": "增强礼品卡管理API测试 礼品卡批次管理API 应该验证批量生成参数", "status": "passed", "title": "应该验证批量生成参数", "duration": 0.4011249999998654, "failureMessages": [], "location": {"line": 119, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡批次管理API"], "fullName": "增强礼品卡管理API测试 礼品卡批次管理API 应该获取批次列表", "status": "passed", "title": "应该获取批次列表", "duration": 0.9985839999999371, "failureMessages": [], "location": {"line": 139, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡统计API"], "fullName": "增强礼品卡管理API测试 礼品卡统计API 应该获取礼品卡统计数据", "status": "passed", "title": "应该获取礼品卡统计数据", "duration": 0.9334579999999733, "failureMessages": [], "location": {"line": 178, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "礼品卡统计API"], "fullName": "增强礼品卡管理API测试 礼品卡统计API 应该拒绝非管理员获取统计数据", "status": "passed", "title": "应该拒绝非管理员获取统计数据", "duration": 0.401915999999801, "failureMessages": [], "location": {"line": 217, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "兑换券管理API"], "fullName": "增强礼品卡管理API测试 兑换券管理API 应该成功创建兑换券", "status": "passed", "title": "应该成功创建兑换券", "duration": 0.48204200000009223, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "兑换券管理API"], "fullName": "增强礼品卡管理API测试 兑换券管理API 应该获取兑换券列表", "status": "passed", "title": "应该获取兑换券列表", "duration": 0.42437500000005457, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "兑换券管理API"], "fullName": "增强礼品卡管理API测试 兑换券管理API 应该更新兑换券状态", "status": "passed", "title": "应该更新兑换券状态", "duration": 0.2786660000001575, "failureMessages": [], "location": {"line": 286, "column": 5}, "meta": {}}, {"ancestorTitles": ["增强礼品卡管理API测试", "兑换券管理API"], "fullName": "增强礼品卡管理API测试 兑换券管理API 应该验证兑换券创建参数", "status": "passed", "title": "应该验证兑换券创建参数", "duration": 0.24000000000023647, "failureMessages": [], "location": {"line": 318, "column": 5}, "meta": {}}], "startTime": 1754123932568, "endTime": 1754123932575.4243, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/bitmarket-v1.3.0/test/api/admin/giftcards-enhanced.test.ts"}]}