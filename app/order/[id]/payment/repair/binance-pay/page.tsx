'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'
import { formatUSDT } from '@/lib/utils'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  product: {
    id: string
    title: string
    images: string
    seller: {
      id: string
      name: string
    }
  }
}

export default function RepairBinancePayPage() {
  const router = useRouter()
  const params = useParams()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [paymentProcessing, setPaymentProcessing] = useState(false)
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')
  const [paymentTimeout, setPaymentTimeout] = useState(900) // 15分钟倒计时
  const [pinCode, setPinCode] = useState<string>('')
  const [pinExpiry, setPinExpiry] = useState<Date | null>(null)
  const [userOrderNumber, setUserOrderNumber] = useState<string>('')
  const [pinGenerating, setPinGenerating] = useState(false)

  const orderId = params?.id as string
  const supplementAmount = parseFloat(searchParams?.get('supplementAmount') || '0')
  const useBalance = searchParams?.get('useBalance') === 'true'
  const balanceAmount = parseFloat(searchParams?.get('balanceAmount') || '0')

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
    }
  }, [session, orderId])

  useEffect(() => {
    // 支付倒计时
    if (paymentTimeout > 0) {
      const timer = setTimeout(() => {
        setPaymentTimeout(paymentTimeout - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else {
      // 支付超时，返回补齐支付页面
      alert('支付超时，请重新选择支付方式')
      router.push(`/order/${orderId}/payment/supplement?supplementAmount=${supplementAmount}&useBalance=${useBalance}&balanceAmount=${balanceAmount}`)
    }
  }, [paymentTimeout, orderId, router, supplementAmount, useBalance, balanceAmount])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/products')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      setOrder(orderData)
      generateQRCode(orderData)
      // 自动生成PIN码
      generatePin()

    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const generateQRCode = async (orderData: Order) => {
    try {
      // 这里应该调用币安支付API生成二维码，使用补齐金额
      // 暂时使用占位符
      setQrCodeUrl('/binance-qr-code.png')
    } catch (error) {
      console.error('生成二维码失败:', error)
      alert('生成支付二维码失败，请稍后重试')
    }
  }

  const generatePin = async () => {
    setPinGenerating(true)
    try {
      const response = await fetch(`/api/orders/${orderId}/generate-pin`, {
        method: 'POST',
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setPinCode(data.pinCode)
        setPinExpiry(new Date(data.expiryTime))
      } else {
        const error = await response.json()
        console.error('PIN码生成失败:', error.error || 'PIN码生成失败')
      }
    } catch (error) {
      console.error('生成PIN码失败:', error)
    } finally {
      setPinGenerating(false)
    }
  }

  const handlePaymentConfirm = async () => {
    // 验证输入
    if (!pinCode) {
      alert('PIN码尚未生成，请稍候')
      return
    }

    if (!userOrderNumber.trim()) {
      alert('请输入币安订单号')
      return
    }

    setPaymentProcessing(true)

    try {
      // 调用补齐支付API
      const response = await fetch(`/api/orders/${orderId}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          paymentMethod: 'supplement_binance_pay',
          supplementAmount: supplementAmount,
          useBalance: useBalance,
          balanceAmount: balanceAmount,
          orderNumber: userOrderNumber,
          paymentPin: pinCode.toUpperCase()
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(`补齐支付申请已提交成功！\n\n管理员已收到您的补齐申请，将通过以下信息进行审核：\n- PIN码: ${pinCode}\n- 币安订单号: ${userOrderNumber}\n- 补齐金额: ${formatUSDT(supplementAmount)}\n${useBalance ? `- 保证金扣除: ${formatUSDT(balanceAmount)}` : ''}\n\n审核通过后您的订单将继续处理。`)

        // 跳转回订单页面并显示待验证状态
        router.push(`/order/${orderId}?status=payment_pending`)
      } else {
        const error = await response.json()
        if (error.error && error.error.includes('保证金余额不足')) {
          alert(`保证金余额不足！\n\n您的补齐申请需要扣除保证金 ${formatUSDT(balanceAmount)}，但您当前的可用保证金不足。\n\n请先充值保证金或联系客服处理。`)
        } else {
          alert(error.error || '支付确认失败')
        }
      }
    } catch (error) {
      console.error('支付确认失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setPaymentProcessing(false)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/products')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回商品列表
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold mb-2">币安支付 - 补齐差额</h1>
            <div className="text-lg text-blue-600 font-semibold">
              补齐金额：{formatUSDT(supplementAmount)}
            </div>
            <div className="text-sm text-gray-600 mt-2">
              订单号：{order.orderNumber}
            </div>
          </div>

          {/* 支付明细 */}
          <div className="mb-6">
            <h3 className="font-medium mb-4">支付明细</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>订单总额</span>
                  <span>{formatUSDT(order.totalAmount)}</span>
                </div>
                {useBalance && (
                  <div className="flex justify-between text-green-600">
                    <span>保证金扣除</span>
                    <span>-{formatUSDT(balanceAmount)}</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-semibold text-lg">
                  <span>需要补齐</span>
                  <span className="text-blue-600">{formatUSDT(supplementAmount)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 支付倒计时 */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-lg">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              请在 {formatTime(paymentTimeout)} 内完成支付
            </div>
          </div>

          {/* 二维码 */}
          <div className="text-center mb-8">
            <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
              {qrCodeUrl ? (
                <img
                  src={qrCodeUrl}
                  alt="支付二维码"
                  className="w-64 h-64 mx-auto"
                />
              ) : (
                <div className="w-64 h-64 flex items-center justify-center bg-gray-100 text-gray-500">
                  生成二维码中...
                </div>
              )}
            </div>
            <p className="text-sm text-gray-600 mt-4">
              请使用币安App扫描上方二维码完成补齐支付
            </p>
          </div>

          {/* PIN码管理 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">支付PIN码</h3>
            <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
              {!pinCode ? (
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-4">
                    正在自动生成支付PIN码...
                  </p>
                  {pinGenerating && (
                    <div className="text-yellow-600">生成中...</div>
                  )}
                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm font-medium text-yellow-800">您的PIN码：</span>
                    <span className="text-2xl font-mono font-bold text-yellow-900 bg-white px-4 py-2 rounded border">
                      {pinCode}
                    </span>
                  </div>
                  <p className="text-xs text-yellow-700">
                    PIN码有效期：{pinExpiry ? new Date(pinExpiry).toLocaleString() : '未知'}
                  </p>
                  <p className="text-xs text-yellow-700 mt-1">
                    <strong>重要提示：</strong>此PIN码用于管理员验证您的支付，请妥善保管
                  </p>
                  <p className="text-xs text-yellow-700 mt-1">
                    管理员将通过此PIN码和您提交的币安订单号来确认您的补齐支付
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* 支付验证 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">支付验证</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  币安订单号 *
                </label>
                <input
                  type="text"
                  value={userOrderNumber}
                  onChange={(e) => setUserOrderNumber(e.target.value)}
                  placeholder="请输入币安支付完成后的订单号"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  完成币安支付后，请将订单号填入此处
                </p>
              </div>
            </div>
          </div>

          {/* 支付说明 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">补齐支付说明</h3>
            <div className="bg-blue-50 rounded-lg p-4">
              <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                <li>系统已自动生成支付PIN码，请记住此PIN码</li>
                <li>打开币安App，点击"扫一扫"</li>
                <li>扫描上方二维码</li>
                <li>确认支付金额为 <strong>{formatUSDT(supplementAmount)}</strong></li>
                <li>完成支付并记录币安订单号</li>
                <li>在下方输入币安订单号</li>
                <li>点击"确认补齐支付"按钮提交验证</li>
                <li>系统将自动扣除您的保证金 <strong>{formatUSDT(balanceAmount)}</strong></li>
                <li>管理员将通过PIN码和订单号审核您的支付</li>
                <li>审核通过后订单将继续处理</li>
              </ol>
              <div className="mt-4 p-3 bg-yellow-100 rounded-lg border border-yellow-300">
                <p className="text-sm text-yellow-800">
                  <strong>注意：</strong>提交后将立即从您的保证金中扣除 {formatUSDT(balanceAmount)}，
                  请确保币安支付已完成再提交订单号。
                </p>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={() => router.push(`/order/${orderId}/payment/supplement?supplementAmount=${supplementAmount}&useBalance=${useBalance}&balanceAmount=${balanceAmount}`)}
              className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
            >
              返回
            </button>
            <button
              onClick={handlePaymentConfirm}
              disabled={paymentProcessing || !pinCode || !userOrderNumber.trim()}
              className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
            >
              {paymentProcessing ? '验证中...' : `确认补齐支付 ${formatUSDT(supplementAmount)}`}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
