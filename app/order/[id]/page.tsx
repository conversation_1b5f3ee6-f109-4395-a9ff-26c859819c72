'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'
import { formatUSDT } from '@/lib/utils'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  paymentMethod: string
  paymentConfirmed: boolean
  paymentTxHash?: string
  paymentPin?: string
  createdAt: string
  paidAt?: string
  shippedAt?: string
  receivedAt?: string
  metadata?: any
  product: {
    id: string
    title: string
    images: string
    seller: {
      id: string
      name: string
    }
  }
  buyer: {
    id: string
    name: string
  }
  seller: {
    id: string
    name: string
  }
}

export default function OrderDetailPage() {
  const router = useRouter()
  const params = useParams()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [showPaymentPendingModal, setShowPaymentPendingModal] = useState(false)

  const orderId = params?.id as string
  const statusParam = searchParams?.get('status')

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
    }
  }, [session, orderId])

  useEffect(() => {
    // 如果URL参数包含payment_pending，显示支付待验证弹窗
    if (statusParam === 'payment_pending') {
      setShowPaymentPendingModal(true)
      // 清除URL参数
      const url = new URL(window.location.href)
      url.searchParams.delete('status')
      window.history.replaceState({}, '', url.toString())
    }
  }, [statusParam])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/products')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      setOrder(orderData)

    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, { text: string; color: string }> = {
      'PENDING_PAYMENT': { text: '待付款', color: 'bg-yellow-100 text-yellow-800' },
      'PAID': { text: '已付款', color: 'bg-blue-100 text-blue-800' },
      'SHIPPED': { text: '已发货', color: 'bg-purple-100 text-purple-800' },
      'COMPLETED': { text: '已完成', color: 'bg-green-100 text-green-800' },
      'CANCELLED': { text: '已取消', color: 'bg-red-100 text-red-800' },
      'REFUND_REQUESTED': { text: '申请退款中', color: 'bg-orange-100 text-orange-800' }
    }
    return statusMap[status] || { text: status, color: 'bg-gray-100 text-gray-800' }
  }

  const getPaymentMethodText = (method: string) => {
    const methodMap: Record<string, string> = {
      'deposit_balance': '保证金支付',
      'binancepay-QRcode': '币安支付',
      'bsc_pay': 'BSC链上支付',
      'supplement_binance_pay': '币安补齐支付',
      'supplement_bsc_pay': 'BSC补齐支付'
    }
    return methodMap[method] || method
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/products')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回商品列表
          </button>
        </div>
      </div>
    )
  }

  const statusInfo = getStatusText(order.status)

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          {/* 订单头部信息 */}
          <div className="border-b pb-6 mb-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h1 className="text-2xl font-bold mb-2">订单详情</h1>
                <p className="text-gray-600">订单号：{order.orderNumber}</p>
              </div>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusInfo.color}`}>
                {statusInfo.text}
              </span>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500">创建时间</span>
                <p className="font-medium">{new Date(order.createdAt).toLocaleString()}</p>
              </div>
              {order.paidAt && (
                <div>
                  <span className="text-gray-500">支付时间</span>
                  <p className="font-medium">{new Date(order.paidAt).toLocaleString()}</p>
                </div>
              )}
              {order.shippedAt && (
                <div>
                  <span className="text-gray-500">发货时间</span>
                  <p className="font-medium">{new Date(order.shippedAt).toLocaleString()}</p>
                </div>
              )}
              {order.receivedAt && (
                <div>
                  <span className="text-gray-500">收货时间</span>
                  <p className="font-medium">{new Date(order.receivedAt).toLocaleString()}</p>
                </div>
              )}
            </div>
          </div>

          {/* 商品信息 */}
          <div className="border-b pb-6 mb-6">
            <h3 className="text-lg font-semibold mb-4">商品信息</h3>
            <div className="flex items-center space-x-4">
              {order.product.images && (
                <img
                  src={order.product.images.split(',')[0]}
                  alt={order.product.title}
                  className="w-20 h-20 object-cover rounded-lg"
                />
              )}
              <div className="flex-1">
                <h4 className="font-medium text-lg">{order.product.title}</h4>
                <p className="text-gray-600">卖家：{order.product.seller.name}</p>
                <p className="text-blue-600 font-semibold">{formatUSDT(order.productPrice)}</p>
              </div>
            </div>
          </div>

          {/* 支付信息 */}
          <div className="border-b pb-6 mb-6">
            <h3 className="text-lg font-semibold mb-4">支付信息</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>商品价格</span>
                <span>{formatUSDT(order.productPrice)}</span>
              </div>
              <div className="flex justify-between">
                <span>运费</span>
                <span>{formatUSDT(order.shippingFee)}</span>
              </div>
              <div className="flex justify-between">
                <span>平台费</span>
                <span>{formatUSDT(order.platformFee)}</span>
              </div>
              <div className="flex justify-between font-semibold text-lg border-t pt-3">
                <span>总计</span>
                <span className="text-blue-600">{formatUSDT(order.totalAmount)}</span>
              </div>
              
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium">支付方式</span>
                  <span>{getPaymentMethodText(order.paymentMethod)}</span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <span className="font-medium">支付状态</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    order.paymentConfirmed ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                  }`}>
                    {order.paymentConfirmed ? '已确认' : '待确认'}
                  </span>
                </div>
                
                {/* 补齐支付信息 */}
                {(order.paymentMethod === 'supplement_binance_pay' || order.paymentMethod === 'supplement_bsc_pay') && order.metadata?.supplementPayment && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-800 mb-2">补齐支付详情</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>补齐金额</span>
                        <span className="font-medium">{formatUSDT(order.metadata.supplementPayment.supplementAmount)}</span>
                      </div>
                      {order.metadata.supplementPayment.useBalance && (
                        <div className="flex justify-between">
                          <span>保证金扣除</span>
                          <span className="font-medium text-green-600">{formatUSDT(order.metadata.supplementPayment.balanceAmount)}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span>支付方式</span>
                        <span>{order.paymentMethod === 'supplement_binance_pay' ? '币安支付' : 'BSC链上支付'}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={() => router.push('/orders')}
              className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
            >
              返回订单列表
            </button>
            
            {order.status === 'PENDING_PAYMENT' && (
              <button
                onClick={() => router.push(`/order/${orderId}/payment/supplement`)}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium"
              >
                继续支付
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 支付待验证弹窗 */}
      {showPaymentPendingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">支付待验证</h3>
              <p className="text-gray-600 mb-6">
                您的补齐支付申请已提交成功！<br/>
                管理员正在审核您的支付信息，<br/>
                审核通过后订单将继续处理。
              </p>
              <button
                onClick={() => setShowPaymentPendingModal(false)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
              >
                我知道了
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
