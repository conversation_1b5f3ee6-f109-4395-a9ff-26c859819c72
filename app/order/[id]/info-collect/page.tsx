'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'
import { formatUSDT } from '@/lib/utils'

interface Address {
  id: string
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  isDefault: boolean
  remark?: string
}

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  product: {
    id: string
    title: string
    images: string
    price: number
    seller: {
      id: string
      name: string
    }
  }
  metadata: {
    quantity: number
    variantId?: string
    itemPrice: number
  }
}

interface ProductVariant {
  id: string
  sku: string
  price: number
  stock: number
  attributes: Array<{
    name: string
    value: string
  }>
}

export default function InfoCollectPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [variant, setVariant] = useState<ProductVariant | null>(null)
  const [addresses, setAddresses] = useState<Address[]>([])
  const [selectedAddressId, setSelectedAddressId] = useState<string>('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [showAddressForm, setShowAddressForm] = useState(false)
  const [editingAddressId, setEditingAddressId] = useState<string>('')
  
  // 新地址表单数据
  const [newAddress, setNewAddress] = useState({
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detail: '',
    remark: '家',
    isDefault: false
  })

  const orderId = params?.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
      loadAddresses()
    }
  }, [session, orderId])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/products')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      setOrder(orderData)
      
      // 如果有变体ID，获取变体信息
      if (orderData.metadata?.variantId) {
        const variantResponse = await fetch(`/api/products/${orderData.product.id}/variants/${orderData.metadata.variantId}`)
        if (variantResponse.ok) {
          const variantData = await variantResponse.json()
          setVariant(variantData)
        }
      }
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const loadAddresses = async () => {
    try {
      const response = await fetch('/api/addresses', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const addressData = await response.json()
        setAddresses(addressData)
        
        // 自动选择默认地址
        const defaultAddress = addressData.find((addr: Address) => addr.isDefault)
        if (defaultAddress) {
          setSelectedAddressId(defaultAddress.id)
        }
      }
    } catch (error) {
      console.error('加载地址失败:', error)
    }
  }

  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingAddressId ? `/api/addresses/${editingAddressId}` : '/api/addresses'
      const method = editingAddressId ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(newAddress)
      })
      
      if (response.ok) {
        const addressData = await response.json()
        
        if (editingAddressId) {
          setAddresses(addresses.map(addr => 
            addr.id === editingAddressId ? addressData : addr
          ))
        } else {
          setAddresses([...addresses, addressData])
          setSelectedAddressId(addressData.id)
        }
        
        setShowAddressForm(false)
        setEditingAddressId('')
        setNewAddress({
          name: '',
          phone: '',
          province: '',
          city: '',
          district: '',
          detail: '',
          remark: '家',
          isDefault: false
        })
      } else {
        const error = await response.json()
        alert(error.error || '保存地址失败')
      }
    } catch (error) {
      console.error('保存地址失败:', error)
      alert('保存地址失败，请稍后重试')
    }
  }

  const handleEditAddress = (address: Address) => {
    setNewAddress({
      name: address.name,
      phone: address.phone,
      province: address.province,
      city: address.city,
      district: address.district,
      detail: address.detail,
      remark: address.remark || '家',
      isDefault: address.isDefault
    })
    setEditingAddressId(address.id)
    setShowAddressForm(true)
  }

  const handleProceedToPayment = async () => {
    if (!selectedAddressId) {
      alert('请选择收货地址')
      return
    }
    
    if (!selectedPaymentMethod) {
      alert('请选择支付方式')
      return
    }
    
    setSubmitting(true)
    
    try {
      // 更新订单信息
      const selectedAddress = addresses.find(addr => addr.id === selectedAddressId)
      
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'update_info',
          shippingAddress: selectedAddress,
          paymentMethod: selectedPaymentMethod,
          status: 'PENDING_PAYMENT'
        })
      })
      
      if (response.ok) {
        // 跳转到支付页面
        router.push(`/order/${orderId}/payment/${selectedPaymentMethod}`)
      } else {
        const error = await response.json()
        alert(error.error || '更新订单失败')
      }
    } catch (error) {
      console.error('更新订单失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/products')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回商品列表
          </button>
        </div>
      </div>
    )
  }

  const itemPrice = variant ? variant.price : order.product.price
  const productPrice = itemPrice * order.metadata.quantity
  const shippingFee = 0 // 商家包邮
  const totalAmount = productPrice + shippingFee

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">订单信息收集</h1>
          
          {/* 商品信息 */}
          <div className="mb-8">
            <h3 className="font-medium mb-4">商品信息</h3>
            <div className="flex items-center space-x-4 p-4 border rounded-lg">
              <img
                src={order.product.images?.split(',')[0] || '/placeholder.jpg'}
                alt={order.product.title}
                className="w-20 h-20 object-cover rounded"
              />
              <div className="flex-1">
                <h4 className="font-medium">{order.product.title}</h4>
                <p className="text-gray-600">卖家：{order.product.seller.name}</p>
                <p className="text-gray-600">数量：{order.metadata.quantity}</p>
                {variant && (
                  <p className="text-gray-600">
                    规格：{variant.attributes.map(attr => `${attr.name}: ${attr.value}`).join(', ')}
                  </p>
                )}
              </div>
              <div className="text-right">
                <p className="text-lg font-semibold text-blue-600">
                  {formatUSDT(itemPrice)}
                </p>
                <p className="text-sm text-gray-600">
                  小计：{formatUSDT(productPrice)}
                </p>
              </div>
            </div>
          </div>

          {/* 收货地址 */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">收货地址</h3>
              <button
                onClick={() => {
                  setNewAddress({
                    name: '',
                    phone: '',
                    province: '',
                    city: '',
                    district: '',
                    detail: '',
                    remark: '家',
                    isDefault: false
                  })
                  setEditingAddressId('')
                  setShowAddressForm(true)
                }}
                className="text-blue-600 hover:text-blue-700 text-sm"
              >
                + 新增地址
              </button>
            </div>

            {addresses.length === 0 && !showAddressForm && (
              <div className="text-center py-8 text-gray-500">
                <p>暂无收货地址，请先添加地址</p>
              </div>
            )}

            {addresses.map((address) => (
              <div
                key={address.id}
                className={`border rounded-lg p-4 mb-3 cursor-pointer transition-colors ${
                  selectedAddressId === address.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedAddressId(address.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      checked={selectedAddressId === address.id}
                      onChange={() => setSelectedAddressId(address.id)}
                      className="text-blue-600"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{address.name}</span>
                        <span className="text-gray-600">{address.phone}</span>
                        {address.remark && (
                          <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                            {address.remark}
                          </span>
                        )}
                        {address.isDefault && (
                          <span className="px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded">
                            默认
                          </span>
                        )}
                      </div>
                      <div className="text-gray-700 mt-1">
                        {address.province} {address.city} {address.district} {address.detail}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEditAddress(address)
                    }}
                    className="text-blue-600 hover:text-blue-700 text-sm"
                  >
                    编辑
                  </button>
                </div>
              </div>
            ))}

            {/* 地址表单 */}
            {showAddressForm && (
              <div className="border rounded-lg p-4 bg-gray-50">
                <h4 className="font-medium mb-4">
                  {editingAddressId ? '编辑地址' : '新增地址'}
                </h4>
                <form onSubmit={handleAddressSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        收货人姓名 *
                      </label>
                      <input
                        type="text"
                        required
                        value={newAddress.name}
                        onChange={(e) => setNewAddress({...newAddress, name: e.target.value})}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        手机号码 *
                      </label>
                      <input
                        type="tel"
                        required
                        value={newAddress.phone}
                        onChange={(e) => setNewAddress({...newAddress, phone: e.target.value})}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        省份 *
                      </label>
                      <input
                        type="text"
                        required
                        value={newAddress.province}
                        onChange={(e) => setNewAddress({...newAddress, province: e.target.value})}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        城市 *
                      </label>
                      <input
                        type="text"
                        required
                        value={newAddress.city}
                        onChange={(e) => setNewAddress({...newAddress, city: e.target.value})}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        区县 *
                      </label>
                      <input
                        type="text"
                        required
                        value={newAddress.district}
                        onChange={(e) => setNewAddress({...newAddress, district: e.target.value})}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      详细地址 *
                    </label>
                    <input
                      type="text"
                      required
                      value={newAddress.detail}
                      onChange={(e) => setNewAddress({...newAddress, detail: e.target.value})}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="街道、门牌号等详细信息"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        地址备注
                      </label>
                      <select
                        value={newAddress.remark}
                        onChange={(e) => setNewAddress({...newAddress, remark: e.target.value})}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="家">家</option>
                        <option value="公司">公司</option>
                        <option value="快递站">快递站</option>
                      </select>
                    </div>
                    <div className="flex items-center">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={newAddress.isDefault}
                          onChange={(e) => setNewAddress({...newAddress, isDefault: e.target.checked})}
                          className="mr-2 text-blue-600"
                        />
                        设为默认地址
                      </label>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      type="submit"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm"
                    >
                      保存地址
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setShowAddressForm(false)
                        setEditingAddressId('')
                      }}
                      className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm"
                    >
                      取消
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>

          {/* 支付方式 */}
          <div className="mb-8">
            <h3 className="font-medium mb-4">支付方式</h3>
            <div className="space-y-3">
              <div
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedPaymentMethod === 'binancepay-QRcode'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedPaymentMethod('binancepay-QRcode')}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    checked={selectedPaymentMethod === 'binancepay-QRcode'}
                    onChange={() => setSelectedPaymentMethod('binancepay-QRcode')}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="font-medium">币安二维码支付</div>
                    <div className="text-sm text-gray-600">使用币安App扫码支付</div>
                  </div>
                </div>
              </div>

              <div
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedPaymentMethod === 'bsc-pay'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedPaymentMethod('bsc-pay')}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    checked={selectedPaymentMethod === 'bsc-pay'}
                    onChange={() => setSelectedPaymentMethod('bsc-pay')}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="font-medium">BNB Smart Chain支付</div>
                    <div className="text-sm text-gray-600">使用BSC网络进行支付</div>
                  </div>
                </div>
              </div>

              <div
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedPaymentMethod === 'balance-pay'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedPaymentMethod('balance-pay')}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    checked={selectedPaymentMethod === 'balance-pay'}
                    onChange={() => setSelectedPaymentMethod('balance-pay')}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="font-medium">余额支付</div>
                    <div className="text-sm text-gray-600">使用账户余额支付</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 费用明细 */}
          <div className="mb-8">
            <h3 className="font-medium mb-4">费用明细</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <span>商品金额</span>
                <span>{formatUSDT(productPrice)}</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span>运费</span>
                <span className="text-green-600">商家包邮</span>
              </div>
              <div className="border-t pt-2 mt-2">
                <div className="flex justify-between items-center font-semibold text-lg">
                  <span>总计</span>
                  <span className="text-blue-600">{formatUSDT(totalAmount)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={() => router.back()}
              className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
            >
              返回
            </button>
            <button
              onClick={handleProceedToPayment}
              disabled={!selectedAddressId || !selectedPaymentMethod || submitting}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
            >
              {submitting ? '处理中...' : '确认并支付'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
