/**
 * 保证金充值API - 支持多种支付方式
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 充值保证金
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const userId = session.user.id
    const { amount, method, txHash, notes } = await request.json()

    // 验证充值金额
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: '充值金额必须大于0' },
        { status: 400 }
      )
    }

    // 验证支付方式
    const validMethods = ['chain', 'binance_qr']
    if (!validMethods.includes(method)) {
      return NextResponse.json(
        { error: '不支持的支付方式' },
        { status: 400 }
      )
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        depositBalance: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    let finalAmount = amount
    let paymentUrl: string | null = null
    let walletAddress: string | null = null
    let paymentPin: string | null = null
    let paymentPinExpiry: Date | null = null

    // 生成PIN码
    const generatePin = () => {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      let pin = ''
      for (let i = 0; i < 8; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        pin += characters[randomIndex]
      }
      return pin
    }

    // 设置PIN码过期时间 (30分钟)
    const pinExpiry = new Date()
    pinExpiry.setMinutes(pinExpiry.getMinutes() + 30)

    // 生成唯一PIN码
    paymentPin = generatePin()
    paymentPinExpiry = pinExpiry

    // 根据支付方式处理
    switch (method) {
      case 'chain':
        // 链上支付，使用USDT钱包地址
        walletAddress = 'TYQEuRHZxZvKJT8PnM8aCdNKhgeVLSbPAU' // USDT-TRC20钱包地址
        break

      case 'binance_qr':
        // 币安扫码支付，使用二维码图片
        paymentUrl = '/binance-qr-code.png' // 币安支付二维码图片路径
        break
    }

    // 创建充值记录
    const depositRecord = await prisma.depositRecord.create({
      data: {
        userId,
        amount: finalAmount,
        originalAmount: amount,
        method,
        txHash: txHash || null,
        notes: notes || null,
        status: 'PENDING', // 所有充值都需要PIN码验证
        pinCode: paymentPin, // 将PIN码同步到pinCode字段
        metadata: {
          userAgent: request.headers.get('user-agent'),
          ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
          paymentUrl,
          walletAddress,
          paymentPin,
          paymentPinExpiry: paymentPinExpiry ? paymentPinExpiry.toISOString() : null
        }
      }
    })

    // 所有充值都需要PIN码验证，不立即更新余额

    // 返回结果
    const response: any = {
      success: true,
      message: getSuccessMessage(method),
      depositId: depositRecord.id,
      amount: finalAmount,
      method,
      paymentPin,
      paymentPinExpiry: paymentPinExpiry ? paymentPinExpiry.toISOString() : null
    }

    if (paymentUrl) {
      response.paymentUrl = paymentUrl
    }

    if (walletAddress) {
      response.walletAddress = walletAddress
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('充值失败:', error)
    return NextResponse.json(
      { error: '充值失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取充值记录
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const userId = session.user.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // 获取充值记录
    const [records, total] = await Promise.all([
      prisma.depositRecord.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          amount: true,
          originalAmount: true,
          method: true,
          status: true,
          txHash: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      prisma.depositRecord.count({
        where: { userId }
      })
    ])

    // 统计信息
    const statistics = await prisma.depositRecord.aggregate({
      where: { 
        userId,
        status: 'COMPLETED'
      },
      _sum: {
        amount: true
      },
      _count: {
        id: true
      }
    })

    return NextResponse.json({
      records,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      statistics: {
        totalAmount: statistics._sum.amount || 0,
        totalCount: statistics._count.id || 0
      }
    })

  } catch (error) {
    console.error('获取充值记录失败:', error)
    return NextResponse.json(
      { error: '获取充值记录失败' },
      { status: 500 }
    )
  }
}

function getSuccessMessage(method: string): string {
  switch (method) {
    case 'chain':
      return '请向指定钱包地址转账USDT，并使用PIN码验证'
    case 'binance_qr':
      return '请使用币安APP扫描二维码支付，并使用PIN码验证'
    default:
      return '充值申请已提交，请使用PIN码验证'
  }
}
