import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import QRCode from 'qrcode'
import { sendOrderStatusNotification } from '@/lib/notifications'

// 创建支付订单
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: orderId } = await params
    const body = await request.json()
    const {
      paymentMethod,
      txHash,
      orderNumber,
      screenshot,
      amount,
      depositAmount,
      supplementAmount,
      supplementMethod,
      paymentPin,
      paymentTxHash,
      useBalance,
      balanceAmount
    } = body

    // 验证订单
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true,
        seller: true,
        product: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 验证用户权限
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user || user.id !== order.buyerId) {
      return NextResponse.json(
        { error: '无权限操作此订单' },
        { status: 403 }
      )
    }

    // 验证订单状态
    if (order.status !== 'PENDING_PAYMENT') {
      return NextResponse.json(
        { error: '订单状态不允许支付' },
        { status: 400 }
      )
    }

    // 根据支付方式处理
    const updateData: any = {
      paymentMethod,
      status: 'PAID'
    }

    if (paymentMethod === 'deposit_balance') {
      // 保证金支付处理
      const userBalance = await prisma.user.findUnique({
        where: { id: user.id },
        select: { depositBalance: true }
      })

      if (!userBalance) {
        return NextResponse.json(
          { error: '用户信息不存在' },
          { status: 404 }
        )
      }

      // 计算可用余额 (简化计算，实际应考虑冻结和待提现)
      const availableBalance = userBalance.depositBalance * 0.9 // 假设90%可用

      if (availableBalance < order.totalAmount) {
        return NextResponse.json(
          {
            error: '保证金余额不足',
            availableBalance: availableBalance,
            requiredAmount: order.totalAmount,
            shortfall: order.totalAmount - availableBalance
          },
          { status: 400 }
        )
      }

      // 扣除保证金
      await prisma.user.update({
        where: { id: user.id },
        data: {
          depositBalance: {
            decrement: order.totalAmount
          }
        }
      })

      // 记录资金交易
      await prisma.fundTransaction.create({
        data: {
          userId: user.id,
          type: 'FREEZE',
          amount: -order.totalAmount,
          description: `订单支付 - ${order.orderNumber}`,
          relatedId: orderId,
          metadata: {
            orderId: orderId,
            orderNumber: order.orderNumber,
            paymentMethod: 'deposit_balance'
          }
        }
      })

      updateData.paymentConfirmed = true
      updateData.paymentTxHash = `DEPOSIT_${Date.now()}`

    } else if (paymentMethod === 'deposit_supplement') {
      // 保证金 + 补充支付处理
      const userBalance = await prisma.user.findUnique({
        where: { id: user.id },
        select: { depositBalance: true }
      })

      if (!userBalance) {
        return NextResponse.json(
          { error: '用户信息不存在' },
          { status: 404 }
        )
      }

      const availableBalance = userBalance.depositBalance * 0.9
      const actualDepositAmount = Math.min(depositAmount || 0, availableBalance)

      if (actualDepositAmount > 0) {
        // 扣除保证金部分
        await prisma.user.update({
          where: { id: user.id },
          data: {
            depositBalance: {
              decrement: actualDepositAmount
            }
          }
        })

        // 记录保证金扣除
        await prisma.fundTransaction.create({
          data: {
            userId: user.id,
            type: 'FREEZE',
            amount: -actualDepositAmount,
            description: `订单支付(保证金部分) - ${order.orderNumber}`,
            relatedId: orderId,
            metadata: {
              orderId: orderId,
              orderNumber: order.orderNumber,
              paymentMethod: 'deposit_supplement',
              supplementMethod: supplementMethod
            }
          }
        })
      }

      updateData.paymentConfirmed = false
      updateData.paymentTxHash = txHash || orderNumber
      updateData.paymentMetadata = {
        depositAmount: actualDepositAmount,
        supplementAmount: supplementAmount,
        supplementMethod: supplementMethod
      }

    } else if (paymentMethod === 'binancepay-QRcode') {
      // 币安支付处理 - 需要PIN码验证
      if (!paymentPin || !orderNumber) {
        return NextResponse.json(
          { error: 'PIN码和订单号不能为空' },
          { status: 400 }
        )
      }

      // 这里应该验证PIN码，但为了简化，我们直接标记为待确认
      updateData.paymentTxHash = orderNumber
      updateData.paymentConfirmed = false
      updateData.status = 'PAID'

    } else if (paymentMethod === 'bsc-pay') {
      // BSC 支付处理 - 使用交易哈希
      if (!paymentTxHash) {
        return NextResponse.json(
          { error: '交易哈希不能为空' },
          { status: 400 }
        )
      }

      updateData.paymentTxHash = paymentTxHash
      updateData.paymentConfirmed = false
      updateData.status = 'PAID'

    } else if (paymentMethod === 'binance_pay') {
      // 兼容旧的Binance Pay 支付处理
      updateData.paymentTxHash = orderNumber || txHash
      updateData.paymentConfirmed = false
    } else if (paymentMethod === 'bnb_chain') {
      // 兼容旧的BNB Chain 支付处理
      updateData.paymentTxHash = txHash
      updateData.paymentConfirmed = false
    } else if (paymentMethod === 'supplement_binance_pay') {
      // 币安补齐支付处理
      if (!orderNumber) {
        return NextResponse.json(
          { error: '币安订单号不能为空' },
          { status: 400 }
        )
      }

      // 验证补齐金额
      if (!supplementAmount || supplementAmount <= 0) {
        return NextResponse.json(
          { error: '补齐金额无效' },
          { status: 400 }
        )
      }

      // 如果使用保证金，需要扣除保证金
      if (useBalance && balanceAmount > 0) {
        const userBalance = await prisma.user.findUnique({
          where: { id: user.id },
          select: { depositBalance: true }
        })

        if (!userBalance) {
          return NextResponse.json(
            { error: '用户信息不存在' },
            { status: 404 }
          )
        }

        // 检查保证金余额是否足够
        if (userBalance.depositBalance < balanceAmount) {
          return NextResponse.json(
            { error: `保证金余额不足。需要扣除 ${balanceAmount} USDT，但您的保证金余额只有 ${userBalance.depositBalance} USDT。请先充值保证金。` },
            { status: 400 }
          )
        }

        // 扣除保证金
        await prisma.user.update({
          where: { id: user.id },
          data: {
            depositBalance: {
              decrement: balanceAmount
            }
          }
        })

        // 记录保证金扣除
        await prisma.fundTransaction.create({
          data: {
            userId: user.id,
            type: 'FREEZE',
            amount: -balanceAmount,
            description: `补齐支付(保证金部分) - ${order.orderNumber}`,
            relatedId: orderId,
            metadata: {
              orderId: orderId,
              orderNumber: order.orderNumber,
              paymentMethod: 'supplement_binance_pay',
              supplementAmount: supplementAmount,
              balanceAmount: balanceAmount
            }
          }
        })
      }

      updateData.paymentMethod = 'supplement_binance_pay'
      updateData.paymentTxHash = orderNumber
      updateData.paymentConfirmed = false
      updateData.status = 'PAID'
      updateData.metadata = {
        ...order.metadata,
        supplementPayment: {
          supplementAmount,
          useBalance,
          balanceAmount,
          paymentType: 'supplement_binance_pay'
        }
      }

    } else if (paymentMethod === 'supplement_bsc_pay') {
      // BSC补齐支付处理
      if (!paymentTxHash) {
        return NextResponse.json(
          { error: '交易哈希不能为空' },
          { status: 400 }
        )
      }

      // 验证补齐金额
      if (!supplementAmount || supplementAmount <= 0) {
        return NextResponse.json(
          { error: '补齐金额无效' },
          { status: 400 }
        )
      }

      // 如果使用保证金，需要扣除保证金
      if (useBalance && balanceAmount > 0) {
        const userBalance = await prisma.user.findUnique({
          where: { id: user.id },
          select: { depositBalance: true }
        })

        if (!userBalance) {
          return NextResponse.json(
            { error: '用户信息不存在' },
            { status: 404 }
          )
        }

        // 检查保证金余额是否足够
        if (userBalance.depositBalance < balanceAmount) {
          return NextResponse.json(
            { error: `保证金余额不足。需要扣除 ${balanceAmount} USDT，但您的保证金余额只有 ${userBalance.depositBalance} USDT。请先充值保证金。` },
            { status: 400 }
          )
        }

        // 扣除保证金
        await prisma.user.update({
          where: { id: user.id },
          data: {
            depositBalance: {
              decrement: balanceAmount
            }
          }
        })

        // 记录保证金扣除
        await prisma.fundTransaction.create({
          data: {
            userId: user.id,
            type: 'FREEZE',
            amount: -balanceAmount,
            description: `补齐支付(保证金部分) - ${order.orderNumber}`,
            relatedId: orderId,
            metadata: {
              orderId: orderId,
              orderNumber: order.orderNumber,
              paymentMethod: 'supplement_bsc_pay',
              supplementAmount: supplementAmount,
              balanceAmount: balanceAmount
            }
          }
        })
      }

      updateData.paymentMethod = 'supplement_bsc_pay'
      updateData.paymentTxHash = paymentTxHash
      updateData.paymentConfirmed = false
      updateData.status = 'PAID'
      updateData.metadata = {
        ...order.metadata,
        supplementPayment: {
          supplementAmount,
          useBalance,
          balanceAmount,
          paymentType: 'supplement_bsc_pay'
        }
      }
    }

    if (screenshot) {
      updateData.paymentScreenshot = screenshot
    }

    // 更新订单
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: updateData,
      include: {
        buyer: true,
        seller: true,
        product: true
      }
    })

    // 创建托管支付记录
    const escrowData: any = {
      orderId: orderId,
      amount: paymentMethod.startsWith('supplement_') ? supplementAmount : order.totalAmount,
      currency: 'USDT',
      status: paymentMethod === 'deposit_balance' ? 'CONFIRMED' : 'PENDING',
      paymentMethod,
      platformFee: order.platformFee,
      networkFee: 0
    }

    if (paymentMethod === 'deposit_balance') {
      escrowData.paymentData = {
        depositAmount: order.totalAmount,
        paymentType: 'deposit_balance'
      }
      escrowData.txHash = updateData.paymentTxHash
    } else if (paymentMethod === 'deposit_supplement') {
      escrowData.paymentData = {
        depositAmount: updateData.paymentMetadata?.depositAmount || 0,
        supplementAmount: updateData.paymentMetadata?.supplementAmount || 0,
        supplementMethod: updateData.paymentMetadata?.supplementMethod,
        txHash: txHash,
        orderNumber: orderNumber,
        screenshot
      }
      escrowData.txHash = txHash || orderNumber
    } else if (paymentMethod === 'supplement_binance_pay') {
      escrowData.paymentData = {
        supplementAmount: supplementAmount,
        useBalance: useBalance,
        balanceAmount: balanceAmount,
        orderNumber: orderNumber,
        paymentType: 'supplement_binance_pay'
      }
      escrowData.txHash = orderNumber
    } else if (paymentMethod === 'supplement_bsc_pay') {
      escrowData.paymentData = {
        supplementAmount: supplementAmount,
        useBalance: useBalance,
        balanceAmount: balanceAmount,
        txHash: paymentTxHash,
        paymentType: 'supplement_bsc_pay'
      }
      escrowData.txHash = paymentTxHash
    } else {
      escrowData.paymentData = {
        txHash: paymentMethod === 'binance_pay' ? orderNumber : txHash,
        orderNumber: paymentMethod === 'binance_pay' ? orderNumber : undefined,
        screenshot
      }
      escrowData.txHash = paymentMethod === 'binance_pay' ? orderNumber : txHash
    }

    await prisma.escrowPayment.create({
      data: escrowData
    })

    // 发送支付状态变化通知
    await sendOrderStatusNotification(
      orderId,
      'PAID',
      order.buyerId,
      order.sellerId
    )

    const successMessage = paymentMethod === 'deposit_balance'
      ? '保证金支付成功，订单已确认'
      : paymentMethod === 'deposit_supplement'
      ? '保证金已扣除，补充支付等待确认'
      : paymentMethod === 'supplement_binance_pay'
      ? '币安补齐支付提交成功，等待确认'
      : paymentMethod === 'supplement_bsc_pay'
      ? 'BSC补齐支付提交成功，等待确认'
      : '支付提交成功，等待确认'

    return NextResponse.json({
      success: true,
      status: updatedOrder.status,
      paymentMethod: updatedOrder.paymentMethod,
      order: updatedOrder,
      message: successMessage
    })

  } catch (error) {
    console.error('Payment processing error:', error)
    return NextResponse.json(
      { error: '支付处理失败' },
      { status: 500 }
    )
  }
}

// 获取支付信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    const { id: orderId } = await params

    // 查找订单的支付记录
    const escrowPayments = await prisma.escrowPayment.findMany({
      where: { orderId: orderId },
      orderBy: { createdAt: 'desc' }
    })

    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        buyer: true,
        seller: true,
        product: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 验证用户权限
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user || (user.id !== order.buyerId && user.id !== order.sellerId)) {
      return NextResponse.json(
        { error: '无权限查看此订单支付信息' },
        { status: 403 }
      )
    }

    // 生成支付二维码（如果需要）
    let paymentQRCode: string | null = null
    if (order.status === 'PENDING_PAYMENT' && user.id === order.buyerId) {
      // 这里可以根据不同的支付方式生成不同的二维码
      const paymentData = {
        orderId: order.id,
        amount: order.totalAmount,
        currency: 'USDT'
      }

      try {
        const qrCodeData = await QRCode.toDataURL(JSON.stringify(paymentData))
        paymentQRCode = qrCodeData
      } catch (qrError) {
        console.error('QR Code generation error:', qrError)
        paymentQRCode = null
      }
    }

    return NextResponse.json({
      order,
      escrowPayments,
      paymentQRCode
    })

  } catch (error) {
    console.error('Get payment info error:', error)
    return NextResponse.json(
      { error: '获取支付信息失败' },
      { status: 500 }
    )
  }
}
