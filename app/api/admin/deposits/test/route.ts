import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 创建测试充值记录
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const {
      amount,
      method,
      status,
      notes,
      pinCode,
      paymentOrderId,
      transactionHash,
      txHash
    } = await request.json()

    // 创建测试充值记录
    const depositRecord = await prisma.depositRecord.create({
      data: {
        userId: admin.id, // 使用管理员作为测试用户
        amount: amount || 100,
        originalAmount: amount || 100,
        method: method || 'chain',
        status: status || 'PENDING',
        notes: notes || '测试充值记录',
        pinCode: pinCode || null,
        paymentOrderId: paymentOrderId || null,
        transactionHash: transactionHash || null,
        txHash: txHash || null,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            depositBalance: true,
            creditScore: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: '测试充值记录创建成功',
      record: depositRecord
    })

  } catch (error) {
    console.error('创建测试充值记录失败:', error)
    return NextResponse.json(
      { error: '创建测试充值记录失败' },
      { status: 500 }
    )
  }
}
