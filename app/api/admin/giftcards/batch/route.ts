import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { Prisma } from '@prisma/client'

// 批量生成礼品卡
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      )
    }

    const { 
      faceValue, 
      quantity, 
      validDays = 365, 
      batchName,
      notes 
    } = await request.json()

    // 验证参数
    if (!faceValue || faceValue <= 0) {
      return NextResponse.json(
        { success: false, error: '面值必须大于0' },
        { status: 400 }
      )
    }

    if (!quantity || quantity <= 0 || quantity > 1000) {
      return NextResponse.json(
        { success: false, error: '数量必须在1-1000之间' },
        { status: 400 }
      )
    }

    // 生成批次ID
    const batchId = `BATCH_${Date.now()}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`
    const validUntil = new Date(Date.now() + validDays * 24 * 60 * 60 * 1000)

    // 批量生成礼品卡
    const giftCards: Prisma.GiftCardCreateManyInput[] = []
    for (let i = 0; i < quantity; i++) {
      // 生成16位随机卡号
      const cardCode = Array.from({length: 16}, () =>
        '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 36)]
      ).join('')

      giftCards.push({
        cardCode,
        faceValue,
        status: 'GENERATED',
        validUntil,
        createdById: session.user.id,
        batchId,
        notes: notes || `批量生成 - ${batchName || batchId}`
      })
    }

    // 使用事务批量插入
    const result = await prisma.$transaction(async (tx) => {
      const createdCards = await tx.giftCard.createMany({
        data: giftCards
      })

      // 获取创建的礼品卡详情
      const cards = await tx.giftCard.findMany({
        where: { batchId },
        include: {
          createdBy: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      return { createdCards, cards }
    })

    return NextResponse.json({
      success: true,
      message: `成功生成 ${quantity} 张礼品卡`,
      data: {
        batchId,
        quantity: result.createdCards.count,
        faceValue,
        validUntil,
        cards: result.cards
      }
    })

  } catch (error) {
    console.error('批量生成礼品卡失败:', error)
    return NextResponse.json(
      { success: false, error: '生成失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取批次列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // 获取批次统计
    const batches = await prisma.giftCard.groupBy({
      by: ['batchId'],
      where: {
        batchId: { not: null }
      },
      _count: {
        id: true
      },
      _sum: {
        faceValue: true
      },
      _min: {
        createdAt: true
      },
      orderBy: {
        _min: {
          createdAt: 'desc'
        }
      },
      skip,
      take: limit
    })

    // 获取每个批次的详细信息
    const batchDetails = await Promise.all(
      batches.map(async (batch) => {
        const statusStats = await prisma.giftCard.groupBy({
          by: ['status'],
          where: { batchId: batch.batchId },
          _count: { id: true }
        })

        const firstCard = await prisma.giftCard.findFirst({
          where: { batchId: batch.batchId },
          include: {
            createdBy: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { createdAt: 'asc' }
        })

        return {
          batchId: batch.batchId,
          totalCards: batch._count.id,
          totalValue: batch._sum.faceValue,
          createdAt: batch._min.createdAt,
          createdBy: firstCard?.createdBy,
          faceValue: firstCard?.faceValue,
          validUntil: firstCard?.validUntil,
          notes: firstCard?.notes,
          statusStats: statusStats.reduce((acc, stat) => {
            acc[stat.status] = stat._count.id
            return acc
          }, {} as Record<string, number>)
        }
      })
    )

    // 获取总批次数
    const totalBatches = await prisma.giftCard.groupBy({
      by: ['batchId'],
      where: {
        batchId: { not: null }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        batches: batchDetails,
        pagination: {
          page,
          limit,
          total: totalBatches.length,
          totalPages: Math.ceil(totalBatches.length / limit)
        }
      }
    })

  } catch (error) {
    console.error('获取批次列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取失败，请稍后重试' },
      { status: 500 }
    )
  }
}
