'use client'

import { useState } from 'react'
import Link from 'next/link'

export default function TestRepairPaymentPage() {
  const [testOrderId, setTestOrderId] = useState('cmdwowqxz00058oft39m7umpd')
  const [supplementAmount, setSupplementAmount] = useState(73)
  const [balanceAmount, setBalanceAmount] = useState(50)
  const [useBalance, setUseBalance] = useState(true)

  const generateTestUrl = (paymentType: 'binance-pay' | 'bsc-pay') => {
    const params = new URLSearchParams({
      supplementAmount: supplementAmount.toString(),
      useBalance: useBalance.toString(),
      balanceAmount: balanceAmount.toString()
    })
    
    return `/order/${testOrderId}/payment/repair/${paymentType}?${params}`
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">补齐支付功能测试</h1>
          
          {/* 测试参数设置 */}
          <div className="space-y-4 mb-8">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                测试订单ID
              </label>
              <input
                type="text"
                value={testOrderId}
                onChange={(e) => setTestOrderId(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="输入订单ID"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  补齐金额 (USDT)
                </label>
                <input
                  type="number"
                  value={supplementAmount}
                  onChange={(e) => setSupplementAmount(Number(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  min="0"
                  step="0.01"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  保证金扣除 (USDT)
                </label>
                <input
                  type="number"
                  value={balanceAmount}
                  onChange={(e) => setBalanceAmount(Number(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="useBalance"
                checked={useBalance}
                onChange={(e) => setUseBalance(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="useBalance" className="ml-2 block text-sm text-gray-900">
                使用保证金抵扣
              </label>
            </div>
          </div>

          {/* 测试链接 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">测试链接</h3>
            
            <div className="grid grid-cols-1 gap-4">
              <Link
                href={generateTestUrl('binance-pay')}
                className="block p-4 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-yellow-800">币安支付补齐</h4>
                    <p className="text-sm text-yellow-600">
                      测试PIN码生成和币安订单号提交功能
                    </p>
                  </div>
                  <div className="text-yellow-600">→</div>
                </div>
              </Link>
              
              <Link
                href={generateTestUrl('bsc-pay')}
                className="block p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-blue-800">BSC链上支付补齐</h4>
                    <p className="text-sm text-blue-600">
                      测试BSC交易哈希提交功能
                    </p>
                  </div>
                  <div className="text-blue-600">→</div>
                </div>
              </Link>
            </div>
          </div>

          {/* 当前参数预览 */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-2">当前测试参数</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p>订单ID: <code className="bg-white px-2 py-1 rounded">{testOrderId}</code></p>
              <p>补齐金额: <span className="font-medium">{supplementAmount} USDT</span></p>
              <p>保证金扣除: <span className="font-medium">{useBalance ? `${balanceAmount} USDT` : '不使用'}</span></p>
              <p>实际需支付: <span className="font-medium text-blue-600">{supplementAmount} USDT</span></p>
            </div>
          </div>

          {/* 管理员测试链接 */}
          <div className="mt-8 p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-medium text-purple-800 mb-2">管理员功能测试</h4>
            <div className="space-y-2">
              <Link
                href={`/admin/orders/${testOrderId}`}
                className="inline-block px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm"
              >
                查看订单详情 (管理员视图)
              </Link>
              <p className="text-sm text-purple-600">
                在管理员页面可以查看PIN码、订单号/交易哈希、保证金扣除等信息
              </p>
            </div>
          </div>

          {/* 返回链接 */}
          <div className="mt-8 pt-4 border-t">
            <Link
              href="/products"
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              ← 返回商品列表
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
