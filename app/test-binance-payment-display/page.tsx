'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Navbar from '@/components/Navbar'

interface DepositRecord {
  id: string
  amount: number
  method: string
  status: string
  txHash?: string
  notes?: string
  createdAt: string
  pinCode?: string
  paymentOrderId?: string
  transactionHash?: string
  user: {
    id: string
    name: string
    email: string
  }
}

export default function TestBinancePaymentDisplayPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [deposits, setDeposits] = useState<DepositRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (session?.user?.role !== 'ADMIN') {
      router.push('/')
      return
    }

    fetchDeposits()
  }, [session, status, router])

  const fetchDeposits = async () => {
    try {
      const response = await fetch('/api/admin/deposits?limit=10')
      if (response.ok) {
        const data = await response.json()
        setDeposits(data.records || [])
      }
    } catch (error) {
      console.error('获取充值记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const createTestDeposit = async (method: 'chain' | 'binance_qr') => {
    setCreating(true)
    try {
      // 创建测试充值记录
      const testData = {
        amount: 100,
        method: method,
        status: 'PENDING',
        notes: `测试${method === 'chain' ? '链上' : '币安'}支付记录`,
        ...(method === 'chain' ? {
          transactionHash: '******************************************',
          txHash: '******************************************'
        } : {
          pinCode: 'ABC123',
          paymentOrderId: 'BINANCE123456789',
          txHash: 'BINANCE123456789'
        })
      }

      const response = await fetch('/api/admin/deposits/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      })

      if (response.ok) {
        alert('测试记录创建成功')
        fetchDeposits()
      } else {
        alert('创建失败')
      }
    } catch (error) {
      console.error('创建测试记录失败:', error)
      alert('创建失败')
    } finally {
      setCreating(false)
    }
  }

  const getMethodDisplay = (method: string) => {
    switch (method) {
      case 'chain':
        return '链上转账'
      case 'binance_qr':
        return '币安扫码支付'
      default:
        return method
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'REJECTED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-6xl mx-auto py-8 px-4">
          <div className="text-center">加载中...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-6xl mx-auto py-8 px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            💰 币安支付显示测试
          </h1>
          
          <div className="mb-6 space-x-4">
            <button
              onClick={() => createTestDeposit('binance_qr')}
              disabled={creating}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {creating ? '创建中...' : '创建币安支付测试记录'}
            </button>
            
            <button
              onClick={() => createTestDeposit('chain')}
              disabled={creating}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {creating ? '创建中...' : '创建链上支付测试记录'}
            </button>
          </div>

          <div className="space-y-4">
            {deposits.map((deposit) => (
              <div key={deposit.id} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">
                      充值记录 #{deposit.id.slice(-8)}
                    </h3>
                    <p className="text-gray-600">
                      {deposit.amount} USDT - {getMethodDisplay(deposit.method)}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(deposit.status)}`}>
                      {deposit.status}
                    </span>
                    <a
                      href={`/admin/deposits/${deposit.id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                    >
                      查看详情
                    </a>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">用户:</span>
                    <span className="ml-2">{deposit.user.name} ({deposit.user.email})</span>
                  </div>
                  
                  <div>
                    <span className="font-medium text-gray-700">创建时间:</span>
                    <span className="ml-2">{new Date(deposit.createdAt).toLocaleString()}</span>
                  </div>

                  {/* 根据支付方式显示不同信息 */}
                  {deposit.method === 'binance_qr' ? (
                    <>
                      {deposit.pinCode && (
                        <div>
                          <span className="font-medium text-gray-700">PIN码:</span>
                          <span className="ml-2 font-mono bg-yellow-100 px-2 py-1 rounded">
                            {deposit.pinCode}
                          </span>
                        </div>
                      )}
                      {(deposit.paymentOrderId || deposit.txHash) && (
                        <div>
                          <span className="font-medium text-gray-700">币安订单号:</span>
                          <span className="ml-2 font-mono text-blue-600">
                            {deposit.paymentOrderId || deposit.txHash}
                          </span>
                        </div>
                      )}
                    </>
                  ) : deposit.method === 'chain' ? (
                    <>
                      {(deposit.transactionHash || deposit.txHash) && (
                        <div className="md:col-span-2">
                          <span className="font-medium text-gray-700">交易哈希:</span>
                          <span className="ml-2 font-mono text-green-600 break-all">
                            {deposit.transactionHash || deposit.txHash}
                          </span>
                        </div>
                      )}
                    </>
                  ) : (
                    <>
                      {deposit.txHash && (
                        <div className="md:col-span-2">
                          <span className="font-medium text-gray-700">支付凭证:</span>
                          <span className="ml-2 font-mono break-all">
                            {deposit.txHash}
                          </span>
                        </div>
                      )}
                    </>
                  )}

                  {deposit.notes && (
                    <div className="md:col-span-2">
                      <span className="font-medium text-gray-700">备注:</span>
                      <span className="ml-2">{deposit.notes}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {deposits.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              暂无充值记录，请创建测试记录
            </div>
          )}

          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">测试说明</h3>
            <div className="text-blue-800 space-y-2 text-sm">
              <p>1. <strong>币安支付</strong>: 显示PIN码和币安订单号，不显示交易哈希</p>
              <p>2. <strong>链上支付</strong>: 显示交易哈希</p>
              <p>3. 点击"查看详情"可以在新标签页中查看管理员详情页面</p>
              <p>4. 验证不同支付方式的信息显示是否正确</p>
              <p>5. 测试URL: http://localhost:3000/admin/deposits/[id]</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
