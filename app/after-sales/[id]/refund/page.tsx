'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'

interface RefundInfo {
  maxRefundAmount: number
  suggestedRefundAmount: number
  availableRefundMethods: Array<{
    code: string
    name: string
    description: string
  }>
  currentStatus: string
  refundAmount?: number
  refundMethod?: string
  refundTxHash?: string
  refundedAt?: string
}

export default function RefundProcessPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [refundInfo, setRefundInfo] = useState<RefundInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  
  // 退款表单
  const [refundForm, setRefundForm] = useState({
    refundAmount: '',
    refundMethod: '',
    confirmPassword: ''
  })

  const requestId = params?.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (requestId) {
      loadRefundInfo()
    }
  }, [session, requestId])

  const loadRefundInfo = async () => {
    try {
      const response = await fetch(`/api/after-sales/${requestId}/refund`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('售后申请不存在')
          router.push('/orders')
          return
        }
        throw new Error('获取退款信息失败')
      }
      
      const data = await response.json()
      setRefundInfo(data)
      
      // 设置默认退款金额
      setRefundForm(prev => ({
        ...prev,
        refundAmount: data.suggestedRefundAmount.toString()
      }))
      
    } catch (error) {
      console.error('加载退款信息失败:', error)
      alert('加载退款信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleProcessRefund = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!refundForm.refundAmount || !refundForm.refundMethod) {
      alert('请填写完整的退款信息')
      return
    }

    const refundAmount = parseFloat(refundForm.refundAmount)
    if (isNaN(refundAmount) || refundAmount <= 0 || refundAmount > (refundInfo?.maxRefundAmount || 0)) {
      alert('退款金额不正确')
      return
    }

    if (!confirm(`确定要退款 ¥${refundAmount.toFixed(2)} 吗？此操作不可撤销。`)) {
      return
    }
    
    setProcessing(true)
    
    try {
      const response = await fetch(`/api/after-sales/${requestId}/refund`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          refundAmount,
          refundMethod: refundForm.refundMethod
        })
      })
      
      if (response.ok) {
        const data = await response.json()
        alert('退款处理成功')
        router.push(`/after-sales/${requestId}/status`)
      } else {
        const error = await response.json()
        alert(error.error || '退款处理失败')
      }
    } catch (error) {
      console.error('退款处理失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setProcessing(false)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': '待处理',
      'APPROVED': '已同意',
      'REJECTED': '已拒绝',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消'
    }
    return statusMap[status] || status
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-lg">加载中...</div>
          </div>
        </div>
      </div>
    )
  }

  if (!refundInfo) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">退款信息不存在</h2>
            <button
              onClick={() => router.push('/orders')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
            >
              返回订单列表
            </button>
          </div>
        </div>
      </div>
    )
  }

  // 如果已经退款完成，显示退款结果
  if (refundInfo.currentStatus === 'COMPLETED' && refundInfo.refundedAt) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="mb-6">
            <div className="flex items-center space-x-4 mb-4">
              <button
                onClick={() => router.back()}
                className="text-blue-600 hover:text-blue-700"
              >
                ← 返回
              </button>
              <h1 className="text-2xl font-bold text-gray-900">退款完成</h1>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">退款已完成</h2>
              <p className="text-gray-600">退款已成功处理，资金将按照选择的方式退回</p>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">退款金额:</span>
                <span className="font-semibold text-green-600">¥{refundInfo.refundAmount?.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">退款方式:</span>
                <span>{refundInfo.availableRefundMethods.find(m => m.code === refundInfo.refundMethod)?.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">退款时间:</span>
                <span>{new Date(refundInfo.refundedAt).toLocaleString()}</span>
              </div>
              {refundInfo.refundTxHash && (
                <div className="flex justify-between">
                  <span className="text-gray-600">交易哈希:</span>
                  <span className="font-mono text-sm">{refundInfo.refundTxHash}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              ← 返回
            </button>
            <h1 className="text-2xl font-bold text-gray-900">处理退款</h1>
          </div>
          <div className="text-sm text-gray-600">
            当前状态：{getStatusText(refundInfo.currentStatus)}
          </div>
        </div>

        {/* 退款信息 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">退款信息</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-600">最大退款金额:</span>
              <span className="ml-2 font-semibold text-blue-600">¥{refundInfo.maxRefundAmount.toFixed(2)}</span>
            </div>
            <div>
              <span className="text-sm text-gray-600">建议退款金额:</span>
              <span className="ml-2 font-semibold text-green-600">¥{refundInfo.suggestedRefundAmount.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* 退款处理表单 */}
        {refundInfo.currentStatus === 'APPROVED' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">退款处理</h3>
            
            <form onSubmit={handleProcessRefund} className="space-y-6">
              {/* 退款金额 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  退款金额 *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">¥</span>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max={refundInfo.maxRefundAmount}
                    value={refundForm.refundAmount}
                    onChange={(e) => setRefundForm({...refundForm, refundAmount: e.target.value})}
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0.00"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  最大退款金额：¥{refundInfo.maxRefundAmount.toFixed(2)}
                </p>
              </div>

              {/* 退款方式 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  退款方式 *
                </label>
                <div className="space-y-3">
                  {refundInfo.availableRefundMethods.map((method) => (
                    <div
                      key={method.code}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        refundForm.refundMethod === method.code
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      onClick={() => setRefundForm({...refundForm, refundMethod: method.code})}
                    >
                      <div className="flex items-center mb-2">
                        <input
                          type="radio"
                          name="refundMethod"
                          value={method.code}
                          checked={refundForm.refundMethod === method.code}
                          onChange={() => setRefundForm({...refundForm, refundMethod: method.code})}
                          className="mr-2"
                        />
                        <span className="font-medium">{method.name}</span>
                      </div>
                      <p className="text-sm text-gray-600">{method.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* 提交按钮 */}
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={processing}
                  className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
                >
                  {processing ? '处理中...' : '确认退款'}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* 状态提示 */}
        {refundInfo.currentStatus !== 'APPROVED' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
              </svg>
              <span className="text-yellow-800">
                {refundInfo.currentStatus === 'PENDING' && '退款申请还未同意，无法处理退款'}
                {refundInfo.currentStatus === 'REJECTED' && '退款申请已被拒绝'}
                {refundInfo.currentStatus === 'PROCESSING' && '退款正在处理中'}
                {refundInfo.currentStatus === 'CANCELLED' && '退款申请已取消'}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
