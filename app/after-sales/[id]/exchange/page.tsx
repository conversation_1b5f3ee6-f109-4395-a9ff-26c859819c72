'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'

interface Product {
  id: string
  title: string
  images: string
  price: number
  stock?: number
}

interface ExchangeInfo {
  request: any
  originalProduct: Product
  exchangeProduct?: Product
  availableProducts: Product[]
  canProcess: boolean
  canComplete: boolean
}

export default function ExchangeProcessPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [exchangeInfo, setExchangeInfo] = useState<ExchangeInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  
  // 换货表单
  const [exchangeForm, setExchangeForm] = useState({
    exchangeProductId: '',
    shippingCompany: '',
    trackingNumber: '',
    exchangeNotes: ''
  })

  const requestId = params?.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (requestId) {
      loadExchangeInfo()
    }
  }, [session, requestId])

  const loadExchangeInfo = async () => {
    try {
      const response = await fetch(`/api/after-sales/${requestId}/exchange`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('售后申请不存在')
          router.push('/orders')
          return
        }
        throw new Error('获取换货信息失败')
      }
      
      const data = await response.json()
      setExchangeInfo(data)
      
    } catch (error) {
      console.error('加载换货信息失败:', error)
      alert('加载换货信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleProcessExchange = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!exchangeForm.exchangeProductId) {
      alert('请选择换货商品')
      return
    }

    if (!confirm('确定要处理此换货申请吗？')) {
      return
    }
    
    setProcessing(true)
    
    try {
      const response = await fetch(`/api/after-sales/${requestId}/exchange`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(exchangeForm)
      })
      
      if (response.ok) {
        alert('换货处理成功')
        loadExchangeInfo() // 重新加载数据
      } else {
        const error = await response.json()
        alert(error.error || '换货处理失败')
      }
    } catch (error) {
      console.error('换货处理失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setProcessing(false)
    }
  }

  const handleCompleteExchange = async () => {
    if (!confirm('确定要完成此换货吗？')) {
      return
    }
    
    setProcessing(true)
    
    try {
      const response = await fetch(`/api/after-sales/${requestId}/exchange`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          exchangeCompleted: true
        })
      })
      
      if (response.ok) {
        alert('换货完成')
        router.push(`/after-sales/${requestId}/status`)
      } else {
        const error = await response.json()
        alert(error.error || '完成换货失败')
      }
    } catch (error) {
      console.error('完成换货失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setProcessing(false)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': '待处理',
      'APPROVED': '已同意',
      'REJECTED': '已拒绝',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消'
    }
    return statusMap[status] || status
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-lg">加载中...</div>
          </div>
        </div>
      </div>
    )
  }

  if (!exchangeInfo) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">换货信息不存在</h2>
            <button
              onClick={() => router.push('/orders')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
            >
              返回订单列表
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              ← 返回
            </button>
            <h1 className="text-2xl font-bold text-gray-900">处理换货</h1>
          </div>
          <div className="text-sm text-gray-600">
            当前状态：{getStatusText(exchangeInfo.request.status)}
          </div>
        </div>

        {/* 原商品信息 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">原商品信息</h3>
          <div className="flex items-center space-x-4">
            <img
              src={exchangeInfo.originalProduct.images?.split(',')[0] || '/placeholder.jpg'}
              alt={exchangeInfo.originalProduct.title}
              className="w-20 h-20 object-cover rounded-lg"
            />
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">{exchangeInfo.originalProduct.title}</h4>
              <div className="text-sm text-gray-600 mt-1">
                价格: ¥{exchangeInfo.originalProduct.price.toFixed(2)}
              </div>
            </div>
          </div>
        </div>

        {/* 换货商品信息（如果已选择） */}
        {exchangeInfo.exchangeProduct && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">换货商品</h3>
            <div className="flex items-center space-x-4">
              <img
                src={exchangeInfo.exchangeProduct.images?.split(',')[0] || '/placeholder.jpg'}
                alt={exchangeInfo.exchangeProduct.title}
                className="w-20 h-20 object-cover rounded-lg"
              />
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{exchangeInfo.exchangeProduct.title}</h4>
                <div className="text-sm text-gray-600 mt-1">
                  价格: ¥{exchangeInfo.exchangeProduct.price.toFixed(2)}
                </div>
              </div>
            </div>
            
            {/* 运单号信息 */}
            {exchangeInfo.request.exchangeTrackingNumber && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <div className="text-sm">
                  <strong>换货运单号:</strong> {exchangeInfo.request.exchangeTrackingNumber}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 换货处理表单（仅卖家且状态为已同意时显示） */}
        {exchangeInfo.canProcess && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">处理换货</h3>
            
            <form onSubmit={handleProcessExchange} className="space-y-6">
              {/* 选择换货商品 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  选择换货商品 *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto">
                  {exchangeInfo.availableProducts.map((product) => (
                    <div
                      key={product.id}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        exchangeForm.exchangeProductId === product.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      onClick={() => setExchangeForm({...exchangeForm, exchangeProductId: product.id})}
                    >
                      <div className="flex items-center space-x-3">
                        <input
                          type="radio"
                          name="exchangeProduct"
                          value={product.id}
                          checked={exchangeForm.exchangeProductId === product.id}
                          onChange={() => setExchangeForm({...exchangeForm, exchangeProductId: product.id})}
                          className="mr-2"
                        />
                        <img
                          src={product.images?.split(',')[0] || '/placeholder.jpg'}
                          alt={product.title}
                          className="w-12 h-12 object-cover rounded"
                        />
                        <div className="flex-1">
                          <h5 className="font-medium text-sm">{product.title}</h5>
                          <div className="text-xs text-gray-600">
                            ¥{product.price.toFixed(2)} | 库存: {product.stock}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 运单号 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  换货运单号
                </label>
                <input
                  type="text"
                  value={exchangeForm.trackingNumber}
                  onChange={(e) => setExchangeForm({...exchangeForm, trackingNumber: e.target.value})}
                  placeholder="请输入换货商品的运单号"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* 换货说明 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  换货说明
                </label>
                <textarea
                  value={exchangeForm.exchangeNotes}
                  onChange={(e) => setExchangeForm({...exchangeForm, exchangeNotes: e.target.value})}
                  placeholder="可填写换货相关说明"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* 提交按钮 */}
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={processing}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
                >
                  {processing ? '处理中...' : '确认换货'}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* 完成换货按钮（处理中状态时显示） */}
        {exchangeInfo.canComplete && exchangeInfo.request.status === 'PROCESSING' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">完成换货</h3>
            <p className="text-gray-600 mb-4">
              请确认已收到退货商品并发出换货商品后，点击完成换货。
            </p>
            <button
              onClick={handleCompleteExchange}
              disabled={processing}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
            >
              {processing ? '处理中...' : '完成换货'}
            </button>
          </div>
        )}

        {/* 状态提示 */}
        {!exchangeInfo.canProcess && !exchangeInfo.canComplete && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
              </svg>
              <span className="text-yellow-800">
                {exchangeInfo.request.status === 'PENDING' && '换货申请还未同意，无法处理换货'}
                {exchangeInfo.request.status === 'REJECTED' && '换货申请已被拒绝'}
                {exchangeInfo.request.status === 'COMPLETED' && '换货已完成'}
                {exchangeInfo.request.status === 'CANCELLED' && '换货申请已取消'}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
