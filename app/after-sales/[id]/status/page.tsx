'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'

interface AfterSalesRequest {
  id: string
  type: string
  reason: string
  description: string
  requestedAmount?: number
  status: string
  createdAt: string
  completedAt?: string
  refundAmount?: number
  refundTxHash?: string
  exchangeTrackingNumber?: string
  repairInstructions?: string
  order: {
    orderNumber: string
    product: {
      title: string
      images: string
    }
  }
}

interface TimelineItem {
  status: string
  timestamp: string
  operator: string
  description: string
}

export default function AfterSalesStatusPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [request, setRequest] = useState<AfterSalesRequest | null>(null)
  const [timeline, setTimeline] = useState<TimelineItem[]>([])
  const [loading, setLoading] = useState(true)

  const requestId = params?.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (requestId) {
      loadStatusData()
    }
  }, [session, requestId])

  const loadStatusData = async () => {
    try {
      const response = await fetch(`/api/after-sales/${requestId}/status`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('售后申请不存在')
          router.push('/orders')
          return
        }
        throw new Error('获取售后状态失败')
      }
      
      const data = await response.json()
      setRequest(data.request)
      setTimeline(data.timeline)
      
    } catch (error) {
      console.error('加载售后状态失败:', error)
      alert('加载售后状态失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const getTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      'REFUND': '退款',
      'EXCHANGE': '换货',
      'REPAIR': '维修'
    }
    return typeMap[type] || type
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': '待处理',
      'APPROVED': '已同意',
      'REJECTED': '已拒绝',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'PENDING': 'text-orange-600 bg-orange-100',
      'APPROVED': 'text-green-600 bg-green-100',
      'REJECTED': 'text-red-600 bg-red-100',
      'PROCESSING': 'text-blue-600 bg-blue-100',
      'COMPLETED': 'text-green-600 bg-green-100',
      'CANCELLED': 'text-gray-600 bg-gray-100'
    }
    return colorMap[status] || 'text-gray-600 bg-gray-100'
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-lg">加载中...</div>
          </div>
        </div>
      </div>
    )
  }

  if (!request) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">售后申请不存在</h2>
            <button
              onClick={() => router.push('/orders')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
            >
              返回订单列表
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              ← 返回
            </button>
            <h1 className="text-2xl font-bold text-gray-900">售后状态跟踪</h1>
          </div>
          <div className="text-sm text-gray-600">
            订单号：{request.order.orderNumber}
          </div>
        </div>

        {/* 售后申请概览 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">售后申请概览</h3>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>
              {getStatusText(request.status)}
            </span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div>
                <span className="text-sm text-gray-600">申请类型:</span>
                <span className="ml-2 font-medium">{getTypeText(request.type)}</span>
              </div>
              <div>
                <span className="text-sm text-gray-600">申请原因:</span>
                <span className="ml-2">{request.reason}</span>
              </div>
              {request.requestedAmount && (
                <div>
                  <span className="text-sm text-gray-600">申请金额:</span>
                  <span className="ml-2 font-medium text-red-600">¥{request.requestedAmount.toFixed(2)}</span>
                </div>
              )}
            </div>
            
            <div className="space-y-3">
              <div>
                <span className="text-sm text-gray-600">申请时间:</span>
                <span className="ml-2">{new Date(request.createdAt).toLocaleString()}</span>
              </div>
              {request.completedAt && (
                <div>
                  <span className="text-sm text-gray-600">完成时间:</span>
                  <span className="ml-2">{new Date(request.completedAt).toLocaleString()}</span>
                </div>
              )}
              {request.refundAmount && (
                <div>
                  <span className="text-sm text-gray-600">实际退款:</span>
                  <span className="ml-2 font-medium text-green-600">¥{request.refundAmount.toFixed(2)}</span>
                </div>
              )}
            </div>
          </div>

          {/* 问题描述 */}
          <div className="mt-4">
            <span className="text-sm text-gray-600">问题描述:</span>
            <div className="mt-2 p-3 bg-gray-50 rounded-lg">
              <p className="text-gray-700">{request.description}</p>
            </div>
          </div>
        </div>

        {/* 处理结果 */}
        {request.status === 'COMPLETED' && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">处理结果</h3>
            
            {request.type === 'REFUND' && request.refundTxHash && (
              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                  </svg>
                  <span className="font-medium text-green-800">退款已完成</span>
                </div>
                <div className="text-sm text-green-700">
                  <p>退款金额：¥{request.refundAmount?.toFixed(2)}</p>
                  <p className="font-mono mt-1">交易哈希：{request.refundTxHash}</p>
                </div>
              </div>
            )}

            {request.type === 'EXCHANGE' && request.exchangeTrackingNumber && (
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <svg className="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                  </svg>
                  <span className="font-medium text-blue-800">换货已发出</span>
                </div>
                <div className="text-sm text-blue-700">
                  <p>运单号：{request.exchangeTrackingNumber}</p>
                </div>
              </div>
            )}

            {request.type === 'REPAIR' && request.repairInstructions && (
              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                  </svg>
                  <span className="font-medium text-yellow-800">维修已完成</span>
                </div>
                <div className="text-sm text-yellow-700">
                  <p>{request.repairInstructions}</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 状态时间线 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">处理进度</h3>
          
          <div className="relative">
            {timeline.map((item, index) => (
              <div key={index} className="flex items-start mb-6 last:mb-0">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white text-sm font-medium">{index + 1}</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-900">{item.description}</h4>
                    <span className="text-sm text-gray-500">
                      {new Date(item.timestamp).toLocaleString()}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">操作人：{item.operator}</p>
                </div>
                {index < timeline.length - 1 && (
                  <div className="absolute left-4 mt-8 w-0.5 h-6 bg-gray-300"></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
