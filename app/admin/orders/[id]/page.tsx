'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import AdminManagementSections from './AdminManagementSections'

interface Order {
  id: string
  status: string
  totalAmount: number
  platformFee: number
  paymentMethod: string
  paymentConfirmed: boolean
  paymentTxHash?: string
  paymentPin?: string
  paymentPinExpiry?: string
  paymentPinUsed: boolean
  verificationAttempts: number
  lastVerificationAt?: string
  refundAmount?: number
  refundReason?: string
  createdAt: string
  paidAt?: string
  shippedAt?: string
  receivedAt?: string
  product: {
    id: string
    title: string
    price: number
    description: string
    imageUrl?: string
  }
  buyer: {
    id: string
    name: string
    email: string
    binanceUid?: string
    bnbWalletAddress?: string
    creditScore: number
    city?: string
    district?: string
  }
  seller: {
    id: string
    name: string
    email: string
    binanceUid?: string
    bnbWalletAddress?: string
    creditScore: number
    city?: string
    district?: string
  }
}

const statusMap = {
  PENDING: { label: '待支付', color: 'bg-yellow-100 text-yellow-800' },
  PAID: { label: '已支付', color: 'bg-blue-100 text-blue-800' },
  SHIPPED: { label: '已发货', color: 'bg-purple-100 text-purple-800' },
  COMPLETED: { label: '已完成', color: 'bg-green-100 text-green-800' },
  CANCELLED: { label: '已取消', color: 'bg-red-100 text-red-800' },
  REFUND_REQUESTED: { label: '申请退款', color: 'bg-orange-100 text-orange-800' }
}

// 简单的toast函数
const toast = {
  success: (message: string) => alert(`成功: ${message}`),
  error: (message: string) => alert(`错误: ${message}`)
}

export default function AdminOrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)
  // 管理功能状态已移至 AdminManagementSections 组件

  const orderId = params?.id as string

  useEffect(() => {
    fetchOrder()
  }, [orderId])

  const fetchOrder = async () => {
    try {
      const response = await fetch(`/api/admin/orders/${orderId}`)
      if (response.ok) {
        const data = await response.json()
        setOrder(data)
      } else {
        toast.error('获取订单详情失败')
      }
    } catch (error) {
      console.error('获取订单详情失败:', error)
      toast.error('获取订单详情失败')
    } finally {
      setLoading(false)
    }
  }

  // 备注和日志获取功能已移至 AdminManagementSections 组件

  const handleAction = async (action: string) => {
    if (!order) return

    const confirmMessages = {
      confirm_payment: '确认支付？',
      approve_refund: '同意退款？',
      reject_refund: '拒绝退款？',
      force_complete: '强制完成订单？',
      force_cancel: '强制取消订单？',
      manual_ship: '手动确认发货？',
      manual_receive: '手动确认收货？'
    }

    if (!confirm(confirmMessages[action as keyof typeof confirmMessages])) {
      return
    }

    setActionLoading(true)
    try {
      const response = await fetch(`/api/admin/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      })

      if (response.ok) {
        const updatedOrder = await response.json()
        setOrder(updatedOrder)
        toast.success('操作成功')
      } else {
        const error = await response.json()
        toast.error(error.error || '操作失败')
      }
    } catch (error) {
      console.error('操作失败:', error)
      toast.error('操作失败')
    } finally {
      setActionLoading(false)
    }
  }

  // 备注添加功能已移至 AdminManagementSections 组件

  // 信用分调整功能已移至 AdminManagementSections 组件

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">订单不存在</div>
      </div>
    )
  }

  const status = statusMap[order.status as keyof typeof statusMap]

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 头部 */}
      <div className="flex items-center gap-4 mb-6">
        <button
          className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          onClick={() => router.push('/admin/orders')}
        >
          ← 返回
        </button>
        <h1 className="text-2xl font-bold">订单详情</h1>
        <span className={`px-2 py-1 rounded-full text-sm ${status.color}`}>
          {status.label}
        </span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：订单信息 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                📦 订单信息
              </h3>
            </div>
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">订单号</label>
                  <p className="font-mono text-sm">{order.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">创建时间</label>
                  <p className="text-sm">{new Date(order.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">支付方式</label>
                  <p className="text-sm">{order.paymentMethod}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">支付确认</label>
                  <p className="text-sm">{order.paymentConfirmed ? '已确认' : '未确认'}</p>
                </div>
              </div>

              {/* PIN码信息 */}
              {order.paymentPin && (
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h4 className="text-sm font-semibold text-yellow-800 mb-3">🔐 PIN码信息</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-yellow-700">PIN码</label>
                      <p className="text-lg font-mono font-bold text-yellow-900 bg-white px-3 py-1 rounded border">
                        {order.paymentPin}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-yellow-700">PIN状态</label>
                      <p className="text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          order.paymentPinUsed
                            ? 'bg-green-100 text-green-800'
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {order.paymentPinUsed ? '已使用' : '未使用'}
                        </span>
                      </p>
                    </div>
                    {order.paymentPinExpiry && (
                      <div>
                        <label className="text-sm font-medium text-yellow-700">PIN过期时间</label>
                        <p className="text-sm text-yellow-800">
                          {new Date(order.paymentPinExpiry).toLocaleString()}
                        </p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-yellow-700">验证尝试次数</label>
                      <p className="text-sm text-yellow-800">
                        {order.verificationAttempts}/5
                      </p>
                    </div>
                    {order.paymentTxHash && (
                      <div className="col-span-2">
                        <label className="text-sm font-medium text-yellow-700">
                          {order.paymentMethod === 'supplement_binance_pay' ? '币安订单号' :
                           order.paymentMethod === 'supplement_bsc_pay' ? 'BSC交易哈希' : '交易标识'}
                        </label>
                        <p className="text-sm font-mono text-yellow-800 bg-white px-3 py-1 rounded border break-all">
                          {order.paymentTxHash}
                        </p>
                      </div>
                    )}
                    {order.lastVerificationAt && (
                      <div className="col-span-2">
                        <label className="text-sm font-medium text-yellow-700">最后验证时间</label>
                        <p className="text-sm text-yellow-800">
                          {new Date(order.lastVerificationAt).toLocaleString()}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 补齐支付信息 */}
              {(order.paymentMethod === 'supplement_binance_pay' || order.paymentMethod === 'supplement_bsc_pay') && order.metadata?.supplementPayment && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-semibold text-blue-800 mb-3">💰 补齐支付信息</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-blue-700">补齐金额</label>
                      <p className="text-lg font-bold text-blue-900">
                        {order.metadata.supplementPayment.supplementAmount} USDT
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-blue-700">保证金扣除</label>
                      <p className="text-lg font-bold text-green-700">
                        {order.metadata.supplementPayment.useBalance ?
                          `${order.metadata.supplementPayment.balanceAmount} USDT` : '未使用'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-blue-700">支付方式</label>
                      <p className="text-sm text-blue-800">
                        {order.paymentMethod === 'supplement_binance_pay' ? '币安支付补齐' : 'BSC链上支付补齐'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-blue-700">支付状态</label>
                      <p className="text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          order.paymentConfirmed
                            ? 'bg-green-100 text-green-800'
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {order.paymentConfirmed ? '已确认' : '待审核'}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {order.paidAt && (
                <div>
                  <label className="text-sm font-medium text-gray-500">支付时间</label>
                  <p className="text-sm">{new Date(order.paidAt).toLocaleString()}</p>
                </div>
              )}

              {order.shippedAt && (
                <div>
                  <label className="text-sm font-medium text-gray-500">发货时间</label>
                  <p className="text-sm">{new Date(order.shippedAt).toLocaleString()}</p>
                </div>
              )}

              {order.receivedAt && (
                <div>
                  <label className="text-sm font-medium text-gray-500">收货时间</label>
                  <p className="text-sm">{new Date(order.receivedAt).toLocaleString()}</p>
                </div>
              )}

              {order.refundReason && (
                <div>
                  <label className="text-sm font-medium text-gray-500">退款原因</label>
                  <p className="text-sm">{order.refundReason}</p>
                </div>
              )}
            </div>
          </div>

          {/* 商品信息 */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">商品信息</h3>
            </div>
            <div className="p-6">
              <div className="flex gap-4">
                {order.product.imageUrl && (
                  <img
                    src={order.product.imageUrl}
                    alt={order.product.title}
                    className="w-20 h-20 object-cover rounded"
                  />
                )}
                <div className="flex-1">
                  <h3 className="font-medium">{order.product.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">{order.product.description}</p>
                  <p className="text-lg font-semibold mt-2">{order.product.price} USDT</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：用户信息和操作 */}
        <div className="space-y-6">
          {/* 买家信息 */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                👤 买家信息
              </h3>
            </div>
            <div className="p-6 space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">用户ID</label>
                <p className="text-sm font-mono">{order.buyer.id}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">姓名</label>
                <p>{order.buyer.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">邮箱</label>
                <p className="text-sm">{order.buyer.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">信用分</label>
                <p>{order.buyer.creditScore}</p>
              </div>
              {order.buyer.binanceUid && (
                <div>
                  <label className="text-sm font-medium text-gray-500">币安UID</label>
                  <p className="text-sm font-mono">{order.buyer.binanceUid}</p>
                </div>
              )}
              {(order.buyer.city || order.buyer.district) && (
                <div>
                  <label className="text-sm font-medium text-gray-500">地区</label>
                  <p className="text-sm">{order.buyer.city} {order.buyer.district}</p>
                </div>
              )}
            </div>
          </div>

          {/* 卖家信息 */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                👤 卖家信息
              </h3>
            </div>
            <div className="p-6 space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">用户ID</label>
                <p className="text-sm font-mono">{order.seller.id}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">姓名</label>
                <p>{order.seller.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">邮箱</label>
                <p className="text-sm">{order.seller.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">信用分</label>
                <p>{order.seller.creditScore}</p>
              </div>
              {order.seller.binanceUid && (
                <div>
                  <label className="text-sm font-medium text-gray-500">币安UID</label>
                  <p className="text-sm font-mono">{order.seller.binanceUid}</p>
                </div>
              )}
              {(order.seller.city || order.seller.district) && (
                <div>
                  <label className="text-sm font-medium text-gray-500">地区</label>
                  <p className="text-sm">{order.seller.city} {order.seller.district}</p>
                </div>
              )}
            </div>
          </div>

          {/* 金额信息 */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                💰 金额信息
              </h3>
            </div>
            <div className="p-6 space-y-3">
              <div className="flex justify-between">
                <span>商品金额</span>
                <span>{order.product.price} USDT</span>
              </div>
              <div className="flex justify-between">
                <span>平台手续费</span>
                <span>{order.platformFee} USDT</span>
              </div>
              <hr className="border-gray-200" />
              <div className="flex justify-between font-semibold">
                <span>总金额</span>
                <span>{order.totalAmount} USDT</span>
              </div>
              {order.refundAmount && (
                <div className="flex justify-between text-red-600">
                  <span>退款金额</span>
                  <span>{order.refundAmount} USDT</span>
                </div>
              )}
            </div>
          </div>

          {/* 管理员操作 */}
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">管理员操作</h3>
            </div>
            <div className="p-6 space-y-2">
              {order.status === 'PAID' && !order.paymentConfirmed && (
                <button
                  onClick={() => handleAction('confirm_payment')}
                  disabled={actionLoading}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  确认支付
                </button>
              )}

              {order.status === 'PAID' && (
                <button
                  onClick={() => handleAction('manual_ship')}
                  disabled={actionLoading}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  手动确认发货
                </button>
              )}

              {order.status === 'SHIPPED' && (
                <button
                  onClick={() => handleAction('manual_receive')}
                  disabled={actionLoading}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  手动确认收货
                </button>
              )}

              {order.status === 'REFUND_REQUESTED' && (
                <>
                  <button
                    onClick={() => handleAction('approve_refund')}
                    disabled={actionLoading}
                    className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                  >
                    同意退款
                  </button>
                  <button
                    onClick={() => handleAction('reject_refund')}
                    disabled={actionLoading}
                    className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    拒绝退款
                  </button>
                </>
              )}

              {['PAID', 'SHIPPED'].includes(order.status) && (
                <button
                  onClick={() => handleAction('force_complete')}
                  disabled={actionLoading}
                  className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  强制完成
                </button>
              )}

              {order.status !== 'COMPLETED' && (
                <button
                  onClick={() => handleAction('force_cancel')}
                  disabled={actionLoading}
                  className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                >
                  强制取消
                </button>
              )}

              <hr className="border-gray-200" />

              {/* 管理功能区域 */}
              <AdminManagementSections
                orderId={orderId}
                order={order}
                onOrderUpdate={fetchOrder}
              />
            </div>
          </div>
        </div>
      </div>






    </div>
  )
}
