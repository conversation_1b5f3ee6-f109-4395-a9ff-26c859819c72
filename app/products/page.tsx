'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import Navbar from '@/components/Navbar'
import { formatUSDT, formatDeposit } from '@/lib/utils'
import Image from 'next/image'
import { getCategoryLabel, getConditionLabel, getCategoryOptions, getConditionOptions } from '@/lib/product-constants'

interface VariantAttribute {
  id: string
  name: string
  value: string
}

interface ProductVariant {
  id: string
  sku?: string
  price: number
  stock: number
  status: string
  isDefault: boolean
  attributes: VariantAttribute[]
}

interface Product {
  id: string
  title: string
  description: string
  images: string | null
  price: number
  category: string
  condition: string
  city: string | null
  district: string | null
  stock: number
  hasVariants: boolean
  variants?: ProductVariant[]
  createdAt: string
  seller: {
    id: string
    userId: string
    name: string | null
    avatar: string | null
    creditScore: number
    depositBalance: number
    city: string | null
    district: string | null
  }
}

interface ProductsResponse {
  products: Product[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
  filters: {
    availableCities: string[]
  }
}

// 格式化商品价格显示
const formatProductPrice = (product: Product) => {
  if (!product.hasVariants || !product.variants || product.variants.length === 0) {
    return formatUSDT(product.price)
  }

  const prices = product.variants.map(v => v.price)
  const minPrice = Math.min(...prices)
  const maxPrice = Math.max(...prices)

  if (minPrice === maxPrice) {
    return formatUSDT(minPrice)
  }

  return `${formatUSDT(minPrice)} - ${formatUSDT(maxPrice)}`
}

// 获取商品库存信息
const getProductStock = (product: Product) => {
  if (!product.hasVariants || !product.variants || product.variants.length === 0) {
    return product.stock
  }

  return product.variants.reduce((total, variant) => total + variant.stock, 0)
}

// 检查商品是否有多个规格
const hasMultipleOptions = (product: Product) => {
  return product.hasVariants && product.variants && product.variants.length > 1
}

export default function ProductsPage() {
  const { data: session } = useSession()
  const [products, setProducts] = useState<Product[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [filters, setFilters] = useState({
    keyword: '',
    city: '',
    category: 'ALL',
    condition: 'ALL',
    sortBy: 'newest'
  })
  const [availableCities, setAvailableCities] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchInput, setSearchInput] = useState('') // 用于搜索输入框

  useEffect(() => {
    fetchProducts()
  }, [pagination.page, filters])

  const fetchProducts = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.keyword && { keyword: filters.keyword }),
        ...(filters.city && { city: filters.city }),
        ...(filters.category !== 'ALL' && { category: filters.category }),
        ...(filters.condition !== 'ALL' && { condition: filters.condition }),
        sortBy: filters.sortBy
      })

      const response = await fetch(`/api/products?${params}`)
      if (response.ok) {
        const data: ProductsResponse = await response.json()
        setProducts(data.products)
        setPagination(data.pagination)
        setAvailableCities(data.filters.availableCities)
      }
    } catch (error) {
      console.error('Failed to fetch products:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setFilters(prev => ({ ...prev, keyword: searchInput.trim() }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleClearSearch = () => {
    setSearchInput('')
    setFilters(prev => ({ ...prev, keyword: '' }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const getConditionText = (condition: string) => {
    return getConditionLabel(condition)
  }

  const getCategoryText = (category: string) => {
    return getCategoryLabel(category)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 统一导航栏 */}
      <Navbar />

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和发布按钮 */}
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">浏览商品</h1>
            {session && (
              <Link
                href="/products/create"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                发布商品
              </Link>
            )}
          </div>

          {/* 搜索和筛选 */}
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <form onSubmit={handleSearch} className="space-y-4">
              {/* 搜索框 */}
              <div className="flex gap-2">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="搜索商品标题..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
                >
                  搜索
                </button>
                {filters.keyword && (
                  <button
                    type="button"
                    onClick={handleClearSearch}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    清除
                  </button>
                )}
              </div>

              {/* 筛选器 */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">城市</label>
                  <select
                    value={filters.city}
                    onChange={(e) => handleFilterChange('city', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">所有城市</option>
                    {availableCities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">分类</label>
                  <select
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    {getCategoryOptions(true).map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">成色</label>
                  <select
                    value={filters.condition}
                    onChange={(e) => handleFilterChange('condition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    {getConditionOptions(undefined, true).map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">排序</label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="newest">最新发布</option>
                    <option value="oldest">最早发布</option>
                    <option value="price_low">价格从低到高</option>
                    <option value="price_high">价格从高到低</option>
                  </select>
                </div>
              </div>

              {/* 当前筛选条件显示 */}
              {(filters.keyword || filters.city || filters.category !== 'ALL' || filters.condition !== 'ALL') && (
                <div className="flex flex-wrap gap-2 pt-2 border-t">
                  <span className="text-sm text-gray-600">当前筛选:</span>
                  {filters.keyword && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      关键词: {filters.keyword}
                      <button
                        type="button"
                        onClick={() => handleFilterChange('keyword', '')}
                        className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  )}
                  {filters.city && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      城市: {filters.city}
                      <button
                        type="button"
                        onClick={() => handleFilterChange('city', '')}
                        className="ml-1 text-green-600 hover:text-green-800"
                      >
                        ×
                      </button>
                    </span>
                  )}
                  {filters.category !== 'ALL' && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      分类: {getCategoryText(filters.category)}
                      <button
                        type="button"
                        onClick={() => handleFilterChange('category', 'ALL')}
                        className="ml-1 text-purple-600 hover:text-purple-800"
                      >
                        ×
                      </button>
                    </span>
                  )}
                  {filters.condition !== 'ALL' && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      成色: {getConditionText(filters.condition)}
                      <button
                        type="button"
                        onClick={() => handleFilterChange('condition', 'ALL')}
                        className="ml-1 text-yellow-600 hover:text-yellow-800"
                      >
                        ×
                      </button>
                    </span>
                  )}
                </div>
              )}
            </form>
          </div>

          {/* 搜索结果统计 */}
          {!isLoading && (
            <div className="mb-4 flex justify-between items-center">
              <div className="text-sm text-gray-600">
                {filters.keyword ? (
                  <>找到 <span className="font-medium">{pagination.total}</span> 个包含 "<span className="font-medium">{filters.keyword}</span>" 的商品</>
                ) : (
                  <>共 <span className="font-medium">{pagination.total}</span> 个商品</>
                )}
              </div>
              <div className="text-sm text-gray-500">
                第 {pagination.page} 页，共 {pagination.pages} 页
              </div>
            </div>
          )}

          {/* 商品列表 */}
          {isLoading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">
                {filters.keyword || filters.city || filters.category !== 'ALL' || filters.condition !== 'ALL'
                  ? '没有找到符合条件的商品'
                  : '暂无商品'
                }
              </div>
              {!filters.keyword && !filters.city && filters.category === 'ALL' && filters.condition === 'ALL' && session && (
                <Link
                  href="/products/create"
                  className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
                >
                  发布第一个商品
                </Link>
              )}
              {(filters.keyword || filters.city || filters.category !== 'ALL' || filters.condition !== 'ALL') && (
                <button
                  onClick={() => {
                    setFilters({
                      keyword: '',
                      city: '',
                      category: 'ALL',
                      condition: 'ALL',
                      sortBy: 'newest'
                    })
                    setSearchInput('')
                    setPagination(prev => ({ ...prev, page: 1 }))
                  }}
                  className="mt-4 inline-block bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md text-sm font-medium"
                >
                  清除所有筛选条件
                </button>
              )}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {products.map((product) => {
                  const firstImage = product.images ? product.images.split(',')[0] : ''
                  return (
                    <Link key={product.id} href={`/products/${product.id}`} className="block">
                      <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        {/* 商品图片 */}
                        <div className="relative h-48">
                          {firstImage ? (
                            <Image
                              src={firstImage}
                              alt={product.title}
                              fill
                              className="object-cover"
                              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                              <div className="text-gray-400 text-4xl">📦</div>
                            </div>
                          )}
                        </div>

                      <div className="p-6">
                        <div className="flex items-center justify-between mb-2">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            {getCategoryText(product.category)}
                          </span>
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            {getConditionText(product.condition)}
                          </span>
                        </div>

                        <h3 className="text-lg font-medium text-gray-900 mb-2 truncate">
                          {product.title}
                        </h3>
                      
                      <p className="text-sm text-gray-600 mb-3 overflow-hidden" style={{display: '-webkit-box', WebkitLineClamp: 3, WebkitBoxOrient: 'vertical'}}>
                        {product.description}
                      </p>
                      
                      <div className="mb-3">
                        <span className="text-2xl font-bold text-blue-600">
                          {formatProductPrice(product)}
                        </span>
                        {hasMultipleOptions(product) && (
                          <div className="text-xs text-gray-500 mt-1">
                            多种规格可选
                          </div>
                        )}
                      </div>
                      
                        <div className="space-y-2 text-sm text-gray-500 mb-4">
                          {/* 用户信息行 */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              {/* 用户头像 */}
                              <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                                {product.seller.avatar ? (
                                  <Image
                                    src={product.seller.avatar}
                                    alt={product.seller.name || '用户头像'}
                                    width={32}
                                    height={32}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <div className="w-full h-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                                    {product.seller.name ? product.seller.name.charAt(0).toUpperCase() : 'U'}
                                  </div>
                                )}
                              </div>
                              <span>
                                {product.seller.name || '匿名用户'}
                              </span>
                            </div>
                            <span>
                              信誉: {product.seller.creditScore}分
                            </span>
                          </div>

                          {/* 保证金信息行 */}
                          <div className="flex items-center justify-between">
                            <span>保证金: {formatDeposit(product.seller.depositBalance)}</span>
                            <span>库存: {getProductStock(product)}</span>
                          </div>
                        </div>
                      
                      {product.city && (
                        <div className="text-sm text-gray-500 mb-4">
                          📍 {product.city} {product.district}
                        </div>
                      )}
                      
                        {session && session.user.id !== product.seller.id && (
                          <div className="mt-4">
                            <button
                              onClick={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                                window.location.href = `/products/${product.id}`
                              }}
                              className="w-full bg-green-600 hover:bg-green-700 text-white text-center px-4 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                              立即购买
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                    </Link>
                  )
                })}
              </div>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="mt-8 flex justify-center">
                  <nav className="flex items-center space-x-2">
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: 1 }))}
                      disabled={pagination.page === 1}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      首页
                    </button>

                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                      disabled={pagination.page === 1}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      上一页
                    </button>

                    {/* 页码显示 */}
                    <div className="flex items-center space-x-1">
                      {(() => {
                        const currentPage = pagination.page
                        const totalPages = pagination.pages
                        const pages: React.ReactElement[] = []

                        // 显示逻辑：当前页前后各显示2页
                        const startPage = Math.max(1, currentPage - 2)
                        const endPage = Math.min(totalPages, currentPage + 2)

                        for (let i = startPage; i <= endPage; i++) {
                          pages.push(
                            <button
                              key={i}
                              onClick={() => setPagination(prev => ({ ...prev, page: i }))}
                              className={`px-3 py-2 text-sm font-medium rounded-md ${
                                i === currentPage
                                  ? 'bg-blue-600 text-white'
                                  : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                              }`}
                            >
                              {i}
                            </button>
                          )
                        }

                        return pages
                      })()}
                    </div>

                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                      disabled={pagination.page === pagination.pages}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      下一页
                    </button>

                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.pages }))}
                      disabled={pagination.page === pagination.pages}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      末页
                    </button>
                  </nav>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
