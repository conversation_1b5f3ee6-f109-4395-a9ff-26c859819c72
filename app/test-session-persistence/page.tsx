'use client'

import { useState, useEffect } from 'react'
import { useSession, signIn, signOut } from 'next-auth/react'
import { useSessionPersistence } from '@/hooks/useSessionPersistence'

export default function TestSessionPersistence() {
  const { data: session, status } = useSession()
  const [testResults, setTestResults] = useState<string[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [sessionInfo, setSessionInfo] = useState<any>(null)

  const {
    refreshSession,
    checkSessionValidity,
    lastActivity
  } = useSessionPersistence({
    enableVisibilityCheck: true,
    enableStorageSync: true,
    enableAutoRefresh: true,
    onSessionExpired: () => {
      addTestResult('❌ 会话已过期')
    },
    onSessionRestored: () => {
      addTestResult('✅ 会话已恢复')
    }
  })

  const addTestResult = (result: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestResults(prev => [...prev, `[${timestamp}] ${result}`])
  }

  const clearResults = () => {
    setTestResults([])
  }

  // 获取详细的会话信息
  const fetchSessionInfo = async () => {
    try {
      const response = await fetch('/api/auth/session')
      const data = await response.json()
      setSessionInfo(data)
      return data
    } catch (error) {
      console.error('Failed to fetch session info:', error)
      return null
    }
  }

  // 检查cookies
  const checkCookies = () => {
    const cookies = document.cookie.split(';')
    const authCookies = cookies.filter(cookie => 
      cookie.includes('next-auth') || cookie.includes('session')
    )
    
    addTestResult(`找到 ${authCookies.length} 个认证相关的 cookies`)
    authCookies.forEach((cookie, index) => {
      addTestResult(`Cookie ${index + 1}: ${cookie.trim()}`)
    })
  }

  // 测试会话持久性
  const runPersistenceTest = async () => {
    setIsRunning(true)
    clearResults()
    
    addTestResult('🧪 开始会话持久性测试...')
    
    // 1. 检查当前会话状态
    addTestResult(`当前会话状态: ${status}`)
    if (session?.user) {
      addTestResult(`✅ 用户已登录: ${session.user.email}`)
      addTestResult(`用户ID: ${session.user.id}`)
      addTestResult(`用户角色: ${session.user.role}`)
    } else {
      addTestResult('❌ 用户未登录')
    }

    // 2. 检查cookies
    addTestResult('\n📋 检查认证 Cookies:')
    checkCookies()

    // 3. 获取详细会话信息
    addTestResult('\n🔍 获取详细会话信息:')
    const sessionData = await fetchSessionInfo()
    if (sessionData) {
      addTestResult('✅ 会话API响应正常')
      addTestResult(`会话过期时间: ${sessionData.expires || '未设置'}`)
    } else {
      addTestResult('❌ 会话API响应失败')
    }

    // 4. 测试会话刷新
    addTestResult('\n🔄 测试会话刷新:')
    try {
      await refreshSession()
      addTestResult('✅ 会话刷新成功')
    } catch (error) {
      addTestResult('❌ 会话刷新失败')
    }

    // 5. 测试会话有效性检查
    addTestResult('\n✅ 测试会话有效性检查:')
    try {
      await checkSessionValidity()
      addTestResult('✅ 会话有效性检查完成')
    } catch (error) {
      addTestResult('❌ 会话有效性检查失败')
    }

    // 6. 检查最后活动时间
    addTestResult(`\n⏰ 最后活动时间: ${new Date(lastActivity).toLocaleString()}`)

    // 7. 测试localStorage同步
    addTestResult('\n💾 检查localStorage同步:')
    try {
      const syncData = localStorage.getItem('session-sync')
      if (syncData) {
        const parsed = JSON.parse(syncData)
        addTestResult('✅ 找到会话同步数据')
        addTestResult(`同步状态: ${parsed.hasSession ? '已登录' : '未登录'}`)
        addTestResult(`同步时间: ${new Date(parsed.timestamp).toLocaleString()}`)
      } else {
        addTestResult('❌ 未找到会话同步数据')
      }
    } catch (error) {
      addTestResult('❌ localStorage同步检查失败')
    }

    addTestResult('\n🎉 测试完成!')
    setIsRunning(false)
  }

  // 模拟页面刷新测试
  const testPageRefresh = () => {
    addTestResult('🔄 模拟页面刷新...')
    window.location.reload()
  }

  // 测试多标签页同步
  const testTabSync = () => {
    addTestResult('🔗 测试多标签页同步 - 请在新标签页中打开此页面')
    window.open(window.location.href, '_blank')
  }

  useEffect(() => {
    fetchSessionInfo()
  }, [session])

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            会话持久性测试工具
          </h1>

          {/* 当前状态 */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">当前状态</h2>
            <div className="space-y-1 text-sm">
              <p><strong>会话状态:</strong> {status}</p>
              {session?.user && (
                <>
                  <p><strong>用户邮箱:</strong> {session.user.email}</p>
                  <p><strong>用户ID:</strong> {session.user.id}</p>
                  <p><strong>用户角色:</strong> {session.user.role}</p>
                  <p><strong>是否调解员:</strong> {session.user.isMediator ? '是' : '否'}</p>
                </>
              )}
              {sessionInfo && (
                <p><strong>会话过期时间:</strong> {sessionInfo.expires || '未设置'}</p>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="mb-6 flex flex-wrap gap-3">
            <button
              onClick={runPersistenceTest}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md font-medium"
            >
              {isRunning ? '测试中...' : '运行持久性测试'}
            </button>
            
            <button
              onClick={testPageRefresh}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium"
            >
              测试页面刷新
            </button>
            
            <button
              onClick={testTabSync}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md font-medium"
            >
              测试多标签页同步
            </button>
            
            <button
              onClick={clearResults}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium"
            >
              清除结果
            </button>

            {!session && (
              <button
                onClick={() => signIn()}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md font-medium"
              >
                登录测试
              </button>
            )}

            {session && (
              <button
                onClick={() => signOut()}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium"
              >
                退出登录
              </button>
            )}
          </div>

          {/* 测试结果 */}
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
            <h3 className="text-white font-semibold mb-2">测试结果:</h3>
            <div className="max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-400">点击"运行持久性测试"开始测试...</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 使用说明 */}
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-900 mb-2">测试说明</h3>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• <strong>运行持久性测试:</strong> 检查当前会话状态、cookies、刷新机制等</li>
              <li>• <strong>测试页面刷新:</strong> 刷新页面验证登录状态是否保持</li>
              <li>• <strong>测试多标签页同步:</strong> 在新标签页中测试会话同步</li>
              <li>• <strong>预期行为:</strong> 登录后刷新页面应保持登录状态，多标签页应同步会话状态</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
