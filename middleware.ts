import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// 定义需要认证的路由
const protectedRoutes = [
  '/profile',
  '/orders',
  '/chat',
  '/deposit',
  '/funds',
  '/escrow',
  '/admin',
  '/mediator',
  '/settings',
  '/favorites',
  '/after-sales',
  '/credit',
  '/giftcards',
  '/redeem',
  '/reviews'
]

// 定义管理员专用路由
const adminRoutes = [
  '/admin'
]

// 定义调解员专用路由
const mediatorRoutes = [
  '/mediator'
]

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // 检查管理员路由权限
    if (adminRoutes.some(route => pathname.startsWith(route))) {
      if (!token || token.role !== 'ADMIN') {
        return NextResponse.redirect(new URL('/auth/signin?error=AdminRequired', req.url))
      }
    }

    // 检查调解员路由权限
    if (mediatorRoutes.some(route => pathname.startsWith(route))) {
      if (!token || !token.isMediator) {
        return NextResponse.redirect(new URL('/auth/signin?error=MediatorRequired', req.url))
      }
    }

    // 对于其他受保护的路由，只需要登录即可
    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // 检查是否为受保护的路由
        const isProtectedRoute = protectedRoutes.some(route => 
          pathname.startsWith(route)
        )

        // 如果是受保护的路由，需要有效的token
        if (isProtectedRoute) {
          return !!token
        }

        // 非受保护的路由允许访问
        return true
      },
    },
  }
)

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了以下开头的路径：
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public|logo.jpg).*)',
  ],
}
