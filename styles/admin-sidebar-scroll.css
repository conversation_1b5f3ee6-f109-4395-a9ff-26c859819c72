/* 管理员侧边栏滚动样式 */

/* 自定义滚动条样式 */
.admin-sidebar-scroll {
  /* 设置滚动条宽度 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

/* Webkit浏览器滚动条样式 */
.admin-sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.admin-sidebar-scroll::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.admin-sidebar-scroll::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.admin-sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 滚动条默认显示，但透明度较低 */
.admin-sidebar-scroll::-webkit-scrollbar {
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.admin-sidebar-scroll:hover::-webkit-scrollbar {
  opacity: 1;
}

/* 平滑滚动 */
.admin-sidebar-scroll {
  scroll-behavior: smooth;
}

/* 移动端优化 */
@media (max-width: 1024px) {
  .admin-sidebar-scroll {
    /* 移动端使用更宽的滚动条 */
    scrollbar-width: auto;
  }

  .admin-sidebar-scroll::-webkit-scrollbar {
    width: 8px;
    opacity: 0.5;
  }

  .admin-sidebar-scroll::-webkit-scrollbar-thumb {
    background: #9ca3af;
  }
}

/* 确保导航元素有正确的高度 */
.admin-sidebar-scroll {
  height: 100% !important;
  overflow-y: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.admin-sidebar-container::before,
.admin-sidebar-container::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 10px;
  pointer-events: none;
  z-index: 10;
  transition: opacity 0.2s ease;
}

.admin-sidebar-container::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  opacity: 0;
}

.admin-sidebar-container::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  opacity: 0;
}

.admin-sidebar-container.has-scroll-top::before {
  opacity: 1;
}

.admin-sidebar-container.has-scroll-bottom::after {
  opacity: 1;
}

/* 导航项间距优化 */
.admin-sidebar-scroll .space-y-1 > * + * {
  margin-top: 0.25rem;
}

/* 确保导航项在滚动时有足够的内边距 */
.admin-sidebar-scroll {
  padding-bottom: 1rem;
}

/* 滚动条区域点击优化 */
.admin-sidebar-scroll::-webkit-scrollbar-track {
  cursor: pointer;
}

.admin-sidebar-scroll::-webkit-scrollbar-thumb {
  cursor: pointer;
}
