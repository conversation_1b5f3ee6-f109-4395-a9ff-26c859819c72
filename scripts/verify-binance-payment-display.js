#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('💰 验证币安支付显示修复...\n')

// 1. 检查管理员充值详情页面修改
console.log('📁 1. 检查管理员充值详情页面修改...')

const depositDetailPath = path.join(process.cwd(), 'app/admin/deposits/[id]/page.tsx')
if (fs.existsSync(depositDetailPath)) {
  console.log('✅ app/admin/deposits/[id]/page.tsx')
  
  const content = fs.readFileSync(depositDetailPath, 'utf8')
  
  const checks = [
    { pattern: 'pinCode\\?:', description: 'PIN码字段定义' },
    { pattern: 'paymentOrderId\\?:', description: '币安订单号字段定义' },
    { pattern: 'transactionHash\\?:', description: '交易哈希字段定义' },
    { pattern: 'deposit\\.method === \'binance_qr\'', description: '币安支付判断逻辑' },
    { pattern: 'deposit\\.method === \'chain\'', description: '链上支付判断逻辑' },
    { pattern: '支付PIN码', description: 'PIN码显示标签' },
    { pattern: '币安订单号', description: '币安订单号显示标签' },
    { pattern: '交易哈希', description: '交易哈希显示标签' },
    { pattern: 'bg-yellow-100', description: 'PIN码高亮显示' }
  ]
  
  let allChecksPass = true
  
  checks.forEach(check => {
    const regex = new RegExp(check.pattern)
    if (regex.test(content)) {
      console.log(`   ✅ ${check.description}`)
    } else {
      console.log(`   ❌ ${check.description}`)
      allChecksPass = false
    }
  })
  
  if (allChecksPass) {
    console.log('   🎉 管理员页面修改检查通过')
  } else {
    console.log('   ⚠️ 管理员页面修改存在问题')
  }
  
} else {
  console.log('❌ app/admin/deposits/[id]/page.tsx (文件不存在)')
}

console.log('')

// 2. 检查数据库模型
console.log('🗄️ 2. 检查数据库模型...')

const schemaPath = path.join(process.cwd(), 'prisma/schema.prisma')
if (fs.existsSync(schemaPath)) {
  console.log('✅ prisma/schema.prisma')
  
  const schemaContent = fs.readFileSync(schemaPath, 'utf8')
  
  const dbChecks = [
    { pattern: 'pinCode\\s+String\\?', description: 'pinCode字段' },
    { pattern: 'paymentOrderId\\s+String\\?', description: 'paymentOrderId字段' },
    { pattern: 'transactionHash\\s+String\\?', description: 'transactionHash字段' },
    { pattern: 'txHash\\s+String\\?', description: 'txHash字段' }
  ]
  
  dbChecks.forEach(check => {
    const regex = new RegExp(check.pattern)
    if (regex.test(schemaContent)) {
      console.log(`   ✅ ${check.description}`)
    } else {
      console.log(`   ❌ ${check.description}`)
    }
  })
} else {
  console.log('❌ prisma/schema.prisma (文件不存在)')
}

console.log('')

// 3. 检查测试文件
console.log('🧪 3. 检查测试文件...')

const testFiles = [
  'app/test-binance-payment-display/page.tsx',
  'app/api/admin/deposits/test/route.ts'
]

testFiles.forEach(file => {
  const fullPath = path.join(process.cwd(), file)
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} (文件不存在)`)
  }
})

console.log('')

// 4. 生成测试建议
console.log('🧪 4. 测试建议...')

console.log('📋 建议进行以下测试:')
console.log('')
console.log('1. 访问测试页面:')
console.log('   http://localhost:3000/test-binance-payment-display')
console.log('')
console.log('2. 创建测试记录:')
console.log('   - 点击"创建币安支付测试记录"')
console.log('   - 点击"创建链上支付测试记录"')
console.log('')
console.log('3. 验证显示效果:')
console.log('   - 币安支付应显示PIN码和币安订单号')
console.log('   - 链上支付应显示交易哈希')
console.log('   - 点击"查看详情"验证管理员页面显示')
console.log('')
console.log('4. 测试现有记录:')
console.log('   - 访问现有充值记录详情页面')
console.log('   - 验证不同支付方式的信息显示是否正确')
console.log('')
console.log('5. 测试URL示例:')
console.log('   http://localhost:3000/admin/deposits/cmdu2eg4u000d8oa6qtes6zqs')

console.log('')

// 5. 输出修复摘要
console.log('📊 5. 修复摘要...')
console.log('- 币安支付显示: PIN码 + 币安订单号')
console.log('- 链上支付显示: 交易哈希')
console.log('- 支持字段: pinCode, paymentOrderId, transactionHash, txHash')
console.log('- 界面优化: 根据支付方式动态显示相关信息')
console.log('- PIN码高亮: 使用黄色背景突出显示')
console.log('- 测试工具: 提供测试页面和API创建测试数据')

console.log('')

// 6. 预期效果
console.log('🎯 6. 预期效果...')
console.log('✅ 管理员查看币安支付记录时，看到PIN码和订单号而不是交易哈希')
console.log('✅ 管理员查看链上支付记录时，看到交易哈希')
console.log('✅ PIN码以黄色背景高亮显示，便于识别')
console.log('✅ 信息显示更加准确和有用')
console.log('✅ 便于管理员验证和审核支付')

console.log('')

// 7. 常见问题排查
console.log('🔧 7. 常见问题排查...')
console.log('如果显示仍然不正确:')
console.log('1. 重启开发服务器: npm run dev')
console.log('2. 清除浏览器缓存')
console.log('3. 检查数据库中的实际数据')
console.log('4. 查看浏览器控制台是否有错误')
console.log('5. 确认API返回的数据结构正确')

console.log('\n🎉 币安支付显示修复验证完成！')

process.exit(0)
