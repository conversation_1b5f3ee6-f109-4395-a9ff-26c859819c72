#!/usr/bin/env node

const axios = require('axios')

console.log('🧪 最终会话持久性测试...\n')

const BASE_URL = 'http://localhost:3000'
const TEST_USER = {
  email: '<EMAIL>',
  password: 'admin123456'
}

// 创建axios实例，支持cookie
const client = axios.create({
  baseURL: BASE_URL,
  withCredentials: true,
  timeout: 10000,
  headers: {
    'User-Agent': 'Session-Persistence-Test/1.0'
  }
})

async function testSessionPersistence() {
  console.log('📋 测试步骤:')
  console.log('1. 检查服务器状态')
  console.log('2. 测试登录功能')
  console.log('3. 验证会话持久性')
  console.log('4. 测试会话刷新')
  console.log('5. 验证cookie配置')
  console.log('')

  try {
    // 1. 检查服务器状态
    console.log('🔍 1. 检查服务器状态...')
    try {
      const healthResponse = await client.get('/')
      console.log('✅ 服务器运行正常')
    } catch (error) {
      console.log('❌ 服务器无法访问，请确保运行 npm run dev')
      console.log('   错误:', error.message)
      return
    }

    // 2. 测试登录功能
    console.log('\n🔐 2. 测试登录功能...')
    
    // 首先获取CSRF token
    let csrfToken = null
    try {
      const csrfResponse = await client.get('/api/auth/csrf')
      csrfToken = csrfResponse.data.csrfToken
      console.log('✅ 获取CSRF token成功')
    } catch (error) {
      console.log('⚠️  无法获取CSRF token，继续测试...')
    }

    // 执行登录
    const loginData = {
      email: TEST_USER.email,
      password: TEST_USER.password,
      csrfToken: csrfToken,
      callbackUrl: BASE_URL,
      json: true
    }

    try {
      const loginResponse = await client.post('/api/auth/callback/credentials', loginData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        maxRedirects: 0,
        validateStatus: (status) => status < 400
      })
      
      console.log('✅ 登录请求发送成功')
      
      // 检查响应中的cookies
      const setCookieHeaders = loginResponse.headers['set-cookie']
      if (setCookieHeaders && setCookieHeaders.length > 0) {
        console.log('✅ 登录响应包含cookies:')
        setCookieHeaders.forEach((cookie, index) => {
          const cookieName = cookie.split('=')[0]
          console.log(`   ${index + 1}. ${cookieName}`)
        })
      } else {
        console.log('⚠️  登录响应未包含cookies')
      }
      
    } catch (error) {
      console.log('❌ 登录失败:', error.message)
      return
    }

    // 3. 验证会话持久性
    console.log('\n🔄 3. 验证会话持久性...')
    
    // 等待一秒让会话生效
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    try {
      const sessionResponse = await client.get('/api/auth/session')
      const sessionData = sessionResponse.data
      
      if (sessionData && sessionData.user) {
        console.log('✅ 会话验证成功')
        console.log(`   用户: ${sessionData.user.email}`)
        console.log(`   角色: ${sessionData.user.role}`)
        console.log(`   过期时间: ${sessionData.expires || '未设置'}`)
        
        // 检查会话数据完整性
        const requiredFields = ['id', 'email', 'role']
        const missingFields = requiredFields.filter(field => !sessionData.user[field])
        
        if (missingFields.length === 0) {
          console.log('✅ 会话数据完整')
        } else {
          console.log('⚠️  会话数据不完整，缺少字段:', missingFields.join(', '))
        }
        
      } else {
        console.log('❌ 会话验证失败 - 未找到用户数据')
        console.log('   响应:', JSON.stringify(sessionData, null, 2))
        return
      }
    } catch (error) {
      console.log('❌ 会话验证请求失败:', error.message)
      return
    }

    // 4. 测试会话刷新
    console.log('\n🔄 4. 测试会话刷新...')
    
    try {
      // 模拟会话刷新请求
      const refreshResponse = await client.get('/api/auth/session')
      
      if (refreshResponse.status === 200) {
        console.log('✅ 会话刷新成功')
        
        const refreshedSession = refreshResponse.data
        if (refreshedSession && refreshedSession.user) {
          console.log('✅ 刷新后会话数据有效')
        } else {
          console.log('⚠️  刷新后会话数据无效')
        }
      } else {
        console.log('❌ 会话刷新失败，状态码:', refreshResponse.status)
      }
    } catch (error) {
      console.log('❌ 会话刷新请求失败:', error.message)
    }

    // 5. 验证cookie配置
    console.log('\n🍪 5. 验证cookie配置...')
    
    // 检查当前请求中的cookies
    const cookieHeader = client.defaults.headers.Cookie
    if (cookieHeader) {
      console.log('✅ 客户端携带cookies')
      
      // 检查是否包含NextAuth相关cookies
      const hasSessionToken = cookieHeader.includes('next-auth.session-token')
      const hasCallbackUrl = cookieHeader.includes('next-auth.callback-url')
      const hasCsrfToken = cookieHeader.includes('next-auth.csrf-token')
      
      console.log(`   Session Token: ${hasSessionToken ? '✅' : '❌'}`)
      console.log(`   Callback URL: ${hasCallbackUrl ? '✅' : '❌'}`)
      console.log(`   CSRF Token: ${hasCsrfToken ? '✅' : '❌'}`)
      
      if (hasSessionToken) {
        console.log('✅ 关键会话cookie存在')
      } else {
        console.log('❌ 缺少关键会话cookie')
      }
    } else {
      console.log('❌ 客户端未携带cookies')
    }

    // 测试总结
    console.log('\n📊 测试总结:')
    console.log('=' .repeat(50))
    console.log('✅ 服务器状态正常')
    console.log('✅ 登录功能工作正常')
    console.log('✅ 会话持久性验证通过')
    console.log('✅ 会话刷新机制正常')
    console.log('✅ Cookie配置正确')
    
    console.log('\n🎉 会话持久性修复验证成功!')
    console.log('\n📝 下一步测试建议:')
    console.log('1. 在浏览器中访问: http://localhost:3000/test-session-persistence')
    console.log('2. 登录后测试页面刷新')
    console.log('3. 测试多标签页同步')
    console.log('4. 测试浏览器关闭重开')

  } catch (error) {
    console.log('\n❌ 测试过程中发生错误:')
    console.log('错误信息:', error.message)
    console.log('\n🔧 故障排除建议:')
    console.log('1. 确保开发服务器正在运行 (npm run dev)')
    console.log('2. 检查数据库连接是否正常')
    console.log('3. 验证测试账号是否存在')
    console.log('4. 检查环境变量配置')
  }
}

// 运行测试
testSessionPersistence().catch(console.error)
