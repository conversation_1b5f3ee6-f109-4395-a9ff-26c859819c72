/**
 * 验证币安存款显示修复脚本
 * 检查币安支付的存款记录是否正确显示PIN码和订单号
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function verifyBinanceDepositFix() {
  console.log('🔍 开始验证币安存款显示修复...')
  
  try {
    // 查找币安支付的存款记录
    const binanceDeposits = await prisma.depositRecord.findMany({
      where: {
        method: 'binance_qr'
      },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })

    console.log(`\n📊 找到 ${binanceDeposits.length} 条币安支付存款记录`)

    if (binanceDeposits.length === 0) {
      console.log('ℹ️  没有找到币安支付的存款记录')
      return
    }

    console.log('\n📋 存款记录详情:')
    console.log('=' * 80)

    for (const deposit of binanceDeposits) {
      console.log(`\n🆔 存款ID: ${deposit.id}`)
      console.log(`👤 用户: ${deposit.user.name} (${deposit.user.email})`)
      console.log(`💰 金额: ${deposit.amount} USDT`)
      console.log(`📅 状态: ${deposit.status}`)
      console.log(`🕐 创建时间: ${deposit.createdAt.toLocaleString()}`)
      
      // 检查PIN码
      if (deposit.pinCode) {
        console.log(`✅ PIN码: ${deposit.pinCode}`)
      } else {
        console.log(`❌ PIN码: 未设置`)
        
        // 检查metadata中是否有PIN码
        const metadata = deposit.metadata
        if (metadata && metadata.paymentPin) {
          console.log(`⚠️  PIN码存在于metadata中: ${metadata.paymentPin}`)
          console.log(`🔧 需要数据迁移`)
        }
      }
      
      // 检查订单号
      if (deposit.paymentOrderId) {
        console.log(`✅ 币安订单号: ${deposit.paymentOrderId}`)
      } else if (deposit.txHash) {
        console.log(`⚠️  订单号存储在txHash字段: ${deposit.txHash}`)
        console.log(`🔧 需要数据迁移`)
      } else {
        console.log(`❌ 币安订单号: 未设置`)
      }
      
      console.log('-'.repeat(60))
    }

    // 统计需要修复的记录
    const needsPinFix = binanceDeposits.filter(d => !d.pinCode && d.metadata?.paymentPin).length
    const needsOrderFix = binanceDeposits.filter(d => !d.paymentOrderId && d.txHash).length

    console.log(`\n📈 修复统计:`)
    console.log(`🔧 需要PIN码迁移的记录: ${needsPinFix}`)
    console.log(`🔧 需要订单号迁移的记录: ${needsOrderFix}`)

    if (needsPinFix > 0 || needsOrderFix > 0) {
      console.log(`\n⚠️  发现需要数据迁移的记录，建议运行数据迁移脚本`)
    } else {
      console.log(`\n✅ 所有币安支付记录的数据结构都是正确的`)
    }

  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 数据迁移函数
async function migrateBinanceDepositData() {
  console.log('\n🔄 开始数据迁移...')
  
  try {
    const binanceDeposits = await prisma.depositRecord.findMany({
      where: {
        method: 'binance_qr'
      }
    })

    let pinMigrated = 0
    let orderMigrated = 0

    for (const deposit of binanceDeposits) {
      const updateData = {}
      
      // 迁移PIN码
      if (!deposit.pinCode && deposit.metadata?.paymentPin) {
        updateData.pinCode = deposit.metadata.paymentPin
        pinMigrated++
      }
      
      // 迁移订单号（对于币安支付，txHash字段存储的是订单号）
      if (!deposit.paymentOrderId && deposit.txHash) {
        updateData.paymentOrderId = deposit.txHash
        updateData.txHash = null // 清空txHash，因为这不是真正的交易哈希
        orderMigrated++
      }
      
      if (Object.keys(updateData).length > 0) {
        await prisma.depositRecord.update({
          where: { id: deposit.id },
          data: updateData
        })
        console.log(`✅ 已迁移存款记录: ${deposit.id}`)
      }
    }

    console.log(`\n📊 迁移完成:`)
    console.log(`🔧 PIN码迁移: ${pinMigrated} 条记录`)
    console.log(`🔧 订单号迁移: ${orderMigrated} 条记录`)

  } catch (error) {
    console.error('❌ 数据迁移过程中出现错误:', error)
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  
  if (args.includes('--migrate')) {
    await migrateBinanceDepositData()
  }
  
  await verifyBinanceDepositFix()
}

main().catch(console.error)
