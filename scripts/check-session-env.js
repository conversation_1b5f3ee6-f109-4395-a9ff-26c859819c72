#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🔍 检查会话持久性相关的环境变量配置...\n')

// 检查 .env 文件
const envFiles = ['.env', '.env.local', '.env.development', '.env.production']
let envFound = false
let envVars = {}

for (const envFile of envFiles) {
  const envPath = path.join(process.cwd(), envFile)
  if (fs.existsSync(envPath)) {
    console.log(`✅ 找到环境变量文件: ${envFile}`)
    envFound = true
    
    const content = fs.readFileSync(envPath, 'utf8')
    const lines = content.split('\n')
    
    lines.forEach(line => {
      const trimmed = line.trim()
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=')
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim()
        }
      }
    })
  }
}

if (!envFound) {
  console.log('❌ 未找到任何环境变量文件')
  console.log('请创建 .env.local 文件并配置必要的环境变量\n')
}

// 检查必要的环境变量
const requiredVars = {
  'NEXTAUTH_SECRET': {
    description: 'NextAuth.js 加密密钥',
    required: true,
    example: 'your-super-secret-key-here'
  },
  'NEXTAUTH_URL': {
    description: 'NextAuth.js 基础URL',
    required: true,
    example: 'http://localhost:3000'
  },
  'JWT_SECRET': {
    description: 'JWT 令牌加密密钥',
    required: false,
    example: 'your-jwt-secret-key'
  },
  'DATABASE_URL': {
    description: '数据库连接字符串',
    required: true,
    example: 'mysql://user:password@localhost:3306/database'
  }
}

console.log('\n📋 环境变量检查结果:')
console.log('=' .repeat(50))

let allGood = true

Object.entries(requiredVars).forEach(([key, config]) => {
  const value = envVars[key] || process.env[key]
  const status = value ? '✅' : (config.required ? '❌' : '⚠️')
  
  console.log(`${status} ${key}`)
  console.log(`   描述: ${config.description}`)
  
  if (value) {
    // 隐藏敏感信息
    const displayValue = key.includes('SECRET') || key.includes('PASSWORD') 
      ? '*'.repeat(Math.min(value.length, 20))
      : value.length > 50 
        ? value.substring(0, 47) + '...'
        : value
    console.log(`   当前值: ${displayValue}`)
  } else {
    console.log(`   状态: ${config.required ? '缺失 (必需)' : '缺失 (可选)'}`)
    console.log(`   示例: ${config.example}`)
    if (config.required) {
      allGood = false
    }
  }
  console.log()
})

// 检查 NEXTAUTH_SECRET 强度
if (envVars.NEXTAUTH_SECRET || process.env.NEXTAUTH_SECRET) {
  const secret = envVars.NEXTAUTH_SECRET || process.env.NEXTAUTH_SECRET
  console.log('🔐 NEXTAUTH_SECRET 强度检查:')
  
  if (secret.length < 32) {
    console.log('⚠️  密钥长度较短，建议至少32个字符')
    allGood = false
  } else {
    console.log('✅ 密钥长度符合要求')
  }
  
  if (!/[A-Z]/.test(secret) || !/[a-z]/.test(secret) || !/[0-9]/.test(secret)) {
    console.log('⚠️  建议密钥包含大小写字母和数字')
  } else {
    console.log('✅ 密钥复杂度良好')
  }
  console.log()
}

// 检查 NEXTAUTH_URL 格式
if (envVars.NEXTAUTH_URL || process.env.NEXTAUTH_URL) {
  let url = envVars.NEXTAUTH_URL || process.env.NEXTAUTH_URL
  console.log('🌐 NEXTAUTH_URL 格式检查:')

  // 移除引号
  url = url.replace(/^["']|["']$/g, '')

  try {
    new URL(url)
    console.log('✅ URL 格式正确')

    if (url.endsWith('/')) {
      console.log('⚠️  URL 不应以斜杠结尾')
    }

    if (process.env.NODE_ENV === 'production' && !url.startsWith('https://')) {
      console.log('⚠️  生产环境建议使用 HTTPS')
    }
  } catch (error) {
    console.log('❌ URL 格式无效:', error.message)
    allGood = false
  }
  console.log()
}

// 生成建议的环境变量配置
if (!allGood) {
  console.log('📝 建议的 .env.local 配置:')
  console.log('=' .repeat(50))
  console.log('# NextAuth.js 配置')
  console.log('NEXTAUTH_SECRET="' + generateRandomString(64) + '"')
  console.log('NEXTAUTH_URL="http://localhost:3000"')
  console.log('')
  console.log('# JWT 配置 (可选)')
  console.log('JWT_SECRET="' + generateRandomString(32) + '"')
  console.log('')
  console.log('# 数据库配置')
  console.log('DATABASE_URL="mysql://bitmarket_user:bitmarket_pass_2024@localhost:3306/bitmarket"')
  console.log('')
}

// 总结
console.log('📊 检查总结:')
console.log('=' .repeat(50))
if (allGood) {
  console.log('✅ 所有必需的环境变量都已正确配置')
  console.log('✅ 会话持久性配置应该能正常工作')
} else {
  console.log('❌ 发现配置问题，请修复后重新启动应用')
  console.log('💡 修复步骤:')
  console.log('   1. 创建或更新 .env.local 文件')
  console.log('   2. 添加缺失的环境变量')
  console.log('   3. 重启开发服务器 (npm run dev)')
}

console.log('\n🔧 相关命令:')
console.log('   测试会话持久性: http://localhost:3000/test-session-persistence')
console.log('   重启开发服务器: npm run dev')
console.log('   检查登录功能: npm run test:login')

// 生成随机字符串
function generateRandomString(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}
