#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🔍 验证会话持久性修复实施情况...\n')

// 检查文件是否存在
const filesToCheck = [
  {
    path: 'middleware.ts',
    description: 'NextAuth 中间件',
    required: true
  },
  {
    path: 'hooks/useSessionPersistence.ts',
    description: '会话持久性 Hook',
    required: true
  },
  {
    path: 'app/test-session-persistence/page.tsx',
    description: '会话持久性测试页面',
    required: true
  },
  {
    path: 'lib/auth.ts',
    description: 'NextAuth 配置文件',
    required: true
  },
  {
    path: 'components/providers/session-provider.tsx',
    description: 'SessionProvider 组件',
    required: true
  }
]

console.log('📁 检查关键文件:')
console.log('=' .repeat(50))

let allFilesExist = true

filesToCheck.forEach(file => {
  const fullPath = path.join(process.cwd(), file.path)
  const exists = fs.existsSync(fullPath)
  const status = exists ? '✅' : '❌'
  
  console.log(`${status} ${file.path}`)
  console.log(`   描述: ${file.description}`)
  
  if (exists) {
    const stats = fs.statSync(fullPath)
    console.log(`   大小: ${stats.size} bytes`)
    console.log(`   修改时间: ${stats.mtime.toLocaleString()}`)
  } else {
    console.log(`   状态: 文件不存在`)
    if (file.required) {
      allFilesExist = false
    }
  }
  console.log()
})

// 检查 middleware.ts 配置
if (fs.existsSync('middleware.ts')) {
  console.log('🔧 检查 middleware.ts 配置:')
  console.log('=' .repeat(50))
  
  const middlewareContent = fs.readFileSync('middleware.ts', 'utf8')
  
  const checks = [
    {
      pattern: /withAuth/,
      description: 'NextAuth withAuth 导入',
      required: true
    },
    {
      pattern: /protectedRoutes/,
      description: '受保护路由定义',
      required: true
    },
    {
      pattern: /authorized.*token/,
      description: '授权回调函数',
      required: true
    },
    {
      pattern: /export const config/,
      description: '中间件配置导出',
      required: true
    }
  ]
  
  checks.forEach(check => {
    const found = check.pattern.test(middlewareContent)
    const status = found ? '✅' : '❌'
    console.log(`${status} ${check.description}`)
  })
  console.log()
}

// 检查 lib/auth.ts 配置
if (fs.existsSync('lib/auth.ts')) {
  console.log('🔧 检查 lib/auth.ts 配置:')
  console.log('=' .repeat(50))
  
  const authContent = fs.readFileSync('lib/auth.ts', 'utf8')
  
  const authChecks = [
    {
      pattern: /updateAge.*60.*60/,
      description: '会话更新间隔配置',
      required: true
    },
    {
      pattern: /jwt:\s*\{[\s\S]*maxAge/,
      description: 'JWT 最大有效期配置',
      required: true
    },
    {
      pattern: /trigger.*update/,
      description: 'JWT 刷新逻辑',
      required: true
    },
    {
      pattern: /cookies:\s*\{[\s\S]*sessionToken/,
      description: 'Cookie 配置',
      required: true
    },
    {
      pattern: /debug.*NODE_ENV/,
      description: '调试模式配置',
      required: false
    }
  ]
  
  authChecks.forEach(check => {
    const found = check.pattern.test(authContent)
    const status = found ? '✅' : (check.required ? '❌' : '⚠️')
    console.log(`${status} ${check.description}`)
  })
  console.log()
}

// 检查 useSessionPersistence Hook
if (fs.existsSync('hooks/useSessionPersistence.ts')) {
  console.log('🔧 检查 useSessionPersistence Hook:')
  console.log('=' .repeat(50))
  
  const hookContent = fs.readFileSync('hooks/useSessionPersistence.ts', 'utf8')
  
  const hookChecks = [
    {
      pattern: /visibilitychange/,
      description: '页面可见性监听',
      required: true
    },
    {
      pattern: /addEventListener.*storage/,
      description: '存储事件监听 (多标签页同步)',
      required: true
    },
    {
      pattern: /refreshSession/,
      description: '会话刷新功能',
      required: true
    },
    {
      pattern: /checkSessionValidity/,
      description: '会话有效性检查',
      required: true
    },
    {
      pattern: /localStorage.*session-sync/,
      description: 'localStorage 同步',
      required: true
    }
  ]
  
  hookChecks.forEach(check => {
    const found = check.pattern.test(hookContent)
    const status = found ? '✅' : '❌'
    console.log(`${status} ${check.description}`)
  })
  console.log()
}

// 检查 SessionProvider 更新
if (fs.existsSync('components/providers/session-provider.tsx')) {
  console.log('🔧 检查 SessionProvider 配置:')
  console.log('=' .repeat(50))
  
  const providerContent = fs.readFileSync('components/providers/session-provider.tsx', 'utf8')
  
  const providerChecks = [
    {
      pattern: /navigator\.onLine/,
      description: '网络状态检测',
      required: true
    },
    {
      pattern: /refetchInterval.*isOnline/,
      description: '动态刷新间隔',
      required: true
    },
    {
      pattern: /basePath.*\/api\/auth/,
      description: 'API 基础路径配置',
      required: true
    }
  ]
  
  providerChecks.forEach(check => {
    const found = check.pattern.test(providerContent)
    const status = found ? '✅' : '❌'
    console.log(`${status} ${check.description}`)
  })
  console.log()
}

// 检查 package.json 脚本
if (fs.existsSync('package.json')) {
  console.log('📦 检查 package.json 脚本:')
  console.log('=' .repeat(50))
  
  const packageContent = fs.readFileSync('package.json', 'utf8')
  const packageJson = JSON.parse(packageContent)
  
  const recommendedScripts = {
    'test:session': 'node scripts/verify-session-persistence.js',
    'check:env': 'node scripts/check-session-env.js'
  }
  
  Object.entries(recommendedScripts).forEach(([script, command]) => {
    const exists = packageJson.scripts && packageJson.scripts[script]
    const status = exists ? '✅' : '⚠️'
    console.log(`${status} ${script}`)
    if (!exists) {
      console.log(`   建议添加: "${script}": "${command}"`)
    }
  })
  console.log()
}

// 总结和建议
console.log('📊 验证总结:')
console.log('=' .repeat(50))

if (allFilesExist) {
  console.log('✅ 所有关键文件都已创建')
  console.log('✅ 会话持久性修复已实施')
  
  console.log('\n🧪 测试步骤:')
  console.log('1. 启动开发服务器: npm run dev')
  console.log('2. 访问测试页面: http://localhost:3000/test-session-persistence')
  console.log('3. 登录用户账户')
  console.log('4. 运行持久性测试')
  console.log('5. 测试页面刷新和多标签页同步')
  
  console.log('\n🔧 环境检查:')
  console.log('运行环境检查: node scripts/check-session-env.js')
  
} else {
  console.log('❌ 发现缺失文件，请完成所有修复步骤')
  console.log('\n💡 修复建议:')
  console.log('1. 确保所有文件都已创建')
  console.log('2. 检查文件内容是否正确')
  console.log('3. 重新运行此验证脚本')
}

console.log('\n🔗 相关链接:')
console.log('- 测试页面: http://localhost:3000/test-session-persistence')
console.log('- 登录页面: http://localhost:3000/auth/signin')
console.log('- NextAuth.js 文档: https://next-auth.js.org/')

console.log('\n✨ 预期修复效果:')
console.log('- ✅ 页面刷新后登录状态保持')
console.log('- ✅ 浏览器关闭重开后状态恢复 (在token有效期内)')
console.log('- ✅ 多标签页登录状态同步')
console.log('- ✅ 自动token刷新机制')
console.log('- ✅ 优雅的过期处理')
