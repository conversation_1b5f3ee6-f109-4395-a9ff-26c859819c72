#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🧪 测试保证金支付逻辑修复...\n')

// 检查修复的文件
const filesToCheck = [
  {
    path: 'app/order/[id]/payment/balance-pay/page.tsx',
    description: '保证金支付页面',
    checks: [
      {
        pattern: /showPinInput/,
        description: '支付密码输入状态',
        shouldExist: false,
        message: '已移除支付密码输入逻辑'
      },
      {
        pattern: /paymentPin/,
        description: '支付密码变量',
        shouldExist: false,
        message: '已移除支付密码相关变量'
      },
      {
        pattern: /handlePaymentConfirm/,
        description: '支付确认函数',
        shouldExist: false,
        message: '已移除支付密码确认函数'
      },
      {
        pattern: /直接进行保证金支付/,
        description: '直接支付逻辑',
        shouldExist: true,
        message: '已实现直接保证金支付逻辑'
      },
      {
        pattern: /payment\/supplement/,
        description: '补充支付页面跳转',
        shouldExist: true,
        message: '已实现补充支付页面跳转'
      }
    ]
  },
  {
    path: 'app/order/[id]/payment/supplement/page.tsx',
    description: '补充支付页面',
    checks: [
      {
        pattern: /SupplementPaymentPage/,
        description: '补充支付页面组件',
        shouldExist: true,
        message: '补充支付页面已创建'
      },
      {
        pattern: /paymentMethods/,
        description: '支付方式选择',
        shouldExist: true,
        message: '支付方式选择功能已实现'
      },
      {
        pattern: /deposit_supplement/,
        description: '混合支付方法',
        shouldExist: true,
        message: '混合支付逻辑已实现'
      }
    ]
  },
  {
    path: 'app/api/orders/[id]/payment/route.ts',
    description: 'API支付路由',
    checks: [
      {
        pattern: /shortfall.*order\.totalAmount.*availableBalance/,
        description: '详细错误信息',
        shouldExist: true,
        message: '已增强错误信息返回'
      }
    ]
  }
]

console.log('📁 检查修复文件:')
console.log('=' .repeat(60))

let allChecksPass = true

filesToCheck.forEach(file => {
  const fullPath = path.join(process.cwd(), file.path)
  const exists = fs.existsSync(fullPath)
  
  console.log(`\n📄 ${file.description}`)
  console.log(`   文件: ${file.path}`)
  
  if (!exists) {
    console.log('   ❌ 文件不存在')
    allChecksPass = false
    return
  }
  
  console.log('   ✅ 文件存在')
  
  const content = fs.readFileSync(fullPath, 'utf8')
  
  file.checks.forEach(check => {
    const found = check.pattern.test(content)
    const status = (check.shouldExist && found) || (!check.shouldExist && !found) ? '✅' : '❌'
    
    if (check.shouldExist) {
      console.log(`   ${status} ${check.description}: ${found ? '已实现' : '未找到'}`)
    } else {
      console.log(`   ${status} ${check.description}: ${found ? '仍然存在' : '已移除'}`)
    }
    
    if (found && check.message) {
      console.log(`      💡 ${check.message}`)
    }
    
    if ((check.shouldExist && !found) || (!check.shouldExist && found)) {
      allChecksPass = false
    }
  })
})

console.log('\n🔍 功能验证:')
console.log('=' .repeat(60))

// 检查支付逻辑流程
const balancePayContent = fs.readFileSync('app/order/[id]/payment/balance-pay/page.tsx', 'utf8')

const logicChecks = [
  {
    name: '保证金充足时直接支付',
    check: () => {
      return balancePayContent.includes('余额充足，直接进行保证金支付') &&
             balancePayContent.includes('paymentMethod: \'deposit_balance\'')
    }
  },
  {
    name: '保证金不足时跳转补充支付',
    check: () => {
      return balancePayContent.includes('payment/supplement') &&
             balancePayContent.includes('supplementAmount') &&
             balancePayContent.includes('useBalance')
    }
  },
  {
    name: '移除支付密码相关逻辑',
    check: () => {
      return !balancePayContent.includes('paymentPin') &&
             !balancePayContent.includes('showPinInput') &&
             !balancePayContent.includes('handlePaymentConfirm')
    }
  },
  {
    name: '更新支付说明',
    check: () => {
      return balancePayContent.includes('无需支付密码') &&
             balancePayContent.includes('混合支付方式')
    }
  }
]

logicChecks.forEach(check => {
  const passed = check.check()
  const status = passed ? '✅' : '❌'
  console.log(`${status} ${check.name}`)
  
  if (!passed) {
    allChecksPass = false
  }
})

console.log('\n📊 测试总结:')
console.log('=' .repeat(60))

if (allChecksPass) {
  console.log('✅ 所有检查都通过了！')
  console.log('✅ 保证金支付逻辑修复成功')
  
  console.log('\n🎯 修复效果:')
  console.log('- ✅ 移除了不应该出现的支付密码输入页面')
  console.log('- ✅ 实现了正确的保证金扣除逻辑')
  console.log('- ✅ 添加了其他支付方式选择界面')
  console.log('- ✅ 确保了支付流程的用户体验流畅')
  
  console.log('\n🧪 测试步骤:')
  console.log('1. 启动开发服务器: npm run dev')
  console.log('2. 访问测试页面: http://localhost:3000/order/cmdsliu50002e8o66rnr44rgt/payment/balance-pay')
  console.log('3. 测试保证金充足的情况 - 应该直接支付')
  console.log('4. 测试保证金不足的情况 - 应该跳转到补充支付页面')
  
  console.log('\n📝 预期行为:')
  console.log('- 保证金充足: 直接扣除保证金，完成支付')
  console.log('- 保证金不足: 先扣除可用保证金，然后选择其他支付方式补齐差额')
  console.log('- 无支付密码: 整个流程不需要输入支付密码')
  
} else {
  console.log('❌ 发现问题，请检查修复实施情况')
  console.log('\n💡 故障排除建议:')
  console.log('1. 检查所有文件是否正确修改')
  console.log('2. 确认代码语法没有错误')
  console.log('3. 重新运行此验证脚本')
}

console.log('\n🔗 相关文件:')
console.log('- 保证金支付页面: app/order/[id]/payment/balance-pay/page.tsx')
console.log('- 补充支付页面: app/order/[id]/payment/supplement/page.tsx')
console.log('- API支付路由: app/api/orders/[id]/payment/route.ts')
console.log('- 测试脚本: scripts/test-balance-payment-fix.js')
