import type { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from './prisma'

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user || !user.password) {
          return null
        }

        // 验证密码
        const isPasswordValid = await bcrypt.compare(credentials.password, user.password)

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email || '',
          name: user.name || '',
          userId: user.userId || '',
          role: user.role || 'USER',
          isMediator: user.isMediator || false,
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours - 会话更新间隔
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production' ? process.env.NEXTAUTH_URL?.replace(/https?:\/\//, '') : undefined,
      },
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  callbacks: {
    async jwt({ token, user, account, trigger }) {
      // 初次登录时保存用户信息
      if (user) {
        token.id = user.id
        token.userId = user.userId || ''
        token.role = user.role || 'USER'
        token.isMediator = user.isMediator || false
        token.iat = Math.floor(Date.now() / 1000)
        token.exp = Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30天后过期
      }

      // 检查token是否需要刷新
      if (trigger === 'update' || (token.exp && Date.now() / 1000 > token.exp - 24 * 60 * 60)) {
        // 在token过期前24小时刷新用户信息
        try {
          const user = await prisma.user.findUnique({
            where: { id: token.id as string },
            select: {
              id: true,
              email: true,
              name: true,
              userId: true,
              role: true,
              isMediator: true,
            }
          })

          if (user) {
            token.id = user.id
            token.userId = user.userId || ''
            token.role = user.role || 'USER'
            token.isMediator = user.isMediator || false
            token.exp = Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 重新设置过期时间
          }
        } catch (error) {
          console.error('Token refresh error:', error)
          // 如果刷新失败，保持原有token
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = (token.id as string) || ''
        session.user.userId = (token.userId as string) || ''
        session.user.role = (token.role as string) || 'USER'
        session.user.isMediator = (token.isMediator as boolean) || false

        // 添加过期时间信息
        session.expires = new Date(token.exp * 1000).toISOString()
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // 确保重定向URL是安全的
      if (url.startsWith('/')) return `${baseUrl}${url}`
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    }
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      console.log('User signed in:', user.email)
    },
    async signOut({ session, token }) {
      console.log('User signed out:', token?.email || session?.user?.email)
    },
    async session({ session, token }) {
      // 可以在这里记录会话活动
    }
  },
  debug: process.env.NODE_ENV === 'development',
}
