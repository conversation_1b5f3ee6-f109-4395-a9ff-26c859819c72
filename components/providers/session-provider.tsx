'use client'

import { SessionProvider } from 'next-auth/react'
import type { Session } from 'next-auth'
import type { ReactNode } from 'react'
import { useEffect, useState } from 'react'

interface AuthSessionProviderProps {
  children: ReactNode
  session?: Session | null
}

export default function AuthSessionProvider({
  children,
  session
}: AuthSessionProviderProps) {
  const [isOnline, setIsOnline] = useState(true)

  // 监听网络状态
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // 初始化网络状态
    setIsOnline(navigator.onLine)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return (
    <SessionProvider
      session={session}
      refetchInterval={isOnline ? 5 * 60 : 0} // 在线时5分钟刷新一次，离线时停止刷新
      refetchOnWindowFocus={true}
      refetchWhenOffline={false}
      basePath="/api/auth"
    >
      {children}
    </SessionProvider>
  )
}
